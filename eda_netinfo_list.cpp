/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright The KiCad Developers, see AUTHORS.txt for contributors.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, you may find one here:
 * http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
 * or you may search the http://www.gnu.org website for the version 2 license,
 * or you may write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-   101, USA
 */

#include "eda_netinfo_list.h"
#include "eda_board_data.h"
#include "eda_pad_data.h"
#include "eda_track_data.h"
#include "eda_zone_data.h"
#include "eda_footprint_data.h"
#include <QDebug>
#include <QStringList>
#include <QMutexLocker>
#include <algorithm>
#include <QSet>

// 前向声明或临时最小实现 - QtBoardCommit不在迁移范围内
class QtBoardCommit {
public:
    void removed(EDA_NET_DATA* item) { 
        Q_UNUSED(item)
        // 临时实现：记录被移除的项
    }
};

//==============================================================================
// KiCadNetinfoMapping Implementation
//==============================================================================

KiCadNetinfoMapping::KiCadNetinfoMapping()
    : m_board(nullptr)
{
}

void KiCadNetinfoMapping::setBoard(const EDA_BOARD_DATA* board)
{
    if (m_board == board)
        return;

    m_board = board;
    update();
}

void KiCadNetinfoMapping::update()
{
    if (!m_board)
        return;

    // 收集所有使用的网络代码
    QSet<int> nets;

    // 确保未连接网络代码0被映射为0
    nets.insert(0);

    // 临时实现：收集网络代码
    // 实际实现中需要遍历板上的所有对象
    
    // 准备新的映射
    m_netMapping.clear();

    // 分配连续的网络代码
    int newNetCode = 0;
    for (int net : nets) {
        m_netMapping[net] = newNetCode++;
    }

    notifyMappingUpdated();
}

int KiCadNetinfoMapping::translate(int netCode) const
{
    auto it = m_netMapping.find(netCode);
    if (it != m_netMapping.end()) {
        return it.value();
    }

    // 没有找到映射项，返回原代码
    return netCode;
}

KiCadNetinfoMapping::Iterator KiCadNetinfoMapping::begin() const
{
    return Iterator(m_netMapping.begin(), this);
}

KiCadNetinfoMapping::Iterator KiCadNetinfoMapping::end() const
{
    return Iterator(m_netMapping.end(), this);
}

//==============================================================================
// KiCadNetinfoMapping::Iterator Implementation
//==============================================================================

KiCadNetinfoMapping::Iterator::Iterator(QMap<int, int>::const_iterator iter, const KiCadNetinfoMapping* mapping)
    : m_iterator(iter)
    , m_mapping(mapping)
{
}

const KiCadNetinfoMapping::Iterator& KiCadNetinfoMapping::Iterator::operator++()
{
    ++m_iterator;
    return *this;
}

KiCadNetinfoMapping::Iterator KiCadNetinfoMapping::Iterator::operator++(int)
{
    Iterator ret = *this;
    ++m_iterator;
    return ret;
}

EDA_NET_DATA* KiCadNetinfoMapping::Iterator::operator*() const
{
    if (m_mapping && m_mapping->getBoard()) {
        // 临时实现：需要通过板对象查找网络项
        // return m_mapping->getBoard()->findNet(m_iterator->first);
    }
    return nullptr;
}

EDA_NET_DATA* KiCadNetinfoMapping::Iterator::operator->() const
{
    return operator*();
}

bool KiCadNetinfoMapping::Iterator::operator!=(const Iterator& other) const
{
    return m_iterator != other.m_iterator;
}

bool KiCadNetinfoMapping::Iterator::operator==(const Iterator& other) const
{
    return m_iterator == other.m_iterator;
}

//==============================================================================
// EDA_NETINFO_LIST Implementation
//==============================================================================

EDA_NETINFO_LIST::EDA_NETINFO_LIST(EDA_BOARD_DATA* parent)
    : m_parent(parent)
    , m_newNetCode(0)
    , m_displayNetnamesDirty(false)
{
    ensureUnconnectedNet();
}

EDA_NETINFO_LIST::~EDA_NETINFO_LIST()
{
    clear();
}

EDA_NET_DATA* EDA_NETINFO_LIST::orphanedItem()
{
    static EDA_NET_DATA* g_orphanedItem = nullptr;
    
    if (!g_orphanedItem) {
        g_orphanedItem = new EDA_NET_DATA(nullptr, QString(), UNCONNECTED);
    }
    
    return g_orphanedItem;
}

EDA_NET_DATA* EDA_NETINFO_LIST::getNetItem(int netCode) const
{
    QMutexLocker locker(&m_mutex);
    auto it = m_netCodes.find(netCode);
    return (it != m_netCodes.end()) ? it.value() : nullptr;
}

EDA_NET_DATA* EDA_NETINFO_LIST::getNetItem(const QString& netName) const
{
    QMutexLocker locker(&m_mutex);
    auto it = m_netNames.find(netName);
    return (it != m_netNames.end()) ? it.value() : nullptr;
}

void EDA_NETINFO_LIST::setDisplayNetnamesDirty(bool dirty)
{
    if (m_displayNetnamesDirty == dirty)
        return;

    m_displayNetnamesDirty = dirty;
    if (m_displayNetnamesDirtyChangedCallback) {
        m_displayNetnamesDirtyChangedCallback(dirty);
    }
}

void EDA_NETINFO_LIST::rebuildDisplayNetnames() const
{
    QMutexLocker locker(&m_mutex);
    
    // 构建短网络名称映射
    QMap<QString, QStringList> shortNameMap;
    
    for (auto it = m_netNames.begin(); it != m_netNames.end(); ++it) {
        EDA_NET_DATA* net = it.value();
        shortNameMap[net->getShortNetname()].append(net->getNetname());
    }
    
    // 为每个网络项构建显示名称
    for (auto it = m_netNames.begin(); it != m_netNames.end(); ++it) {
        EDA_NET_DATA* net = it.value();
        const QString& shortName = net->getShortNetname();
        
        if (shortNameMap[shortName].size() == 1) {
            // 短名称唯一，直接使用
            const_cast<EDA_NET_DATA*>(net)->updateDisplayNetname();
        } else {
            // 短名称冲突，需要消歧义
            QStringList parts = net->getNetname().split(QStringLiteral("/"));
            QVector<QStringList> aggregateParts;
            int firstNonCommon = -1;
            
            // 收集所有冲突名称的组成部分
            for (const QString& longName : shortNameMap[shortName]) {
                aggregateParts.append(longName.split(QStringLiteral("/")));
            }
            
            // 找到第一个不相同的部分
            for (int i = 0; i < parts.size() && firstNonCommon == -1; ++i) {
                for (const QStringList& otherParts : aggregateParts) {
                    if (i < otherParts.size() && otherParts[i] == parts[i]) {
                        continue;
                    }
                    firstNonCommon = i;
                    break;
                }
            }
            
            // 构建消歧义名称
            if (firstNonCommon > 0 && firstNonCommon < parts.size()) {
                QStringList disambiguatedParts;
                for (int i = firstNonCommon; i < parts.size(); ++i) {
                    disambiguatedParts.append(parts[i]);
                }
                // 这里需要更新网络项的显示名称
                // const_cast<EDA_NET_DATA*>(net)->setDisplayNetname(unescapeString(disambiguatedParts.join("/")));
            } else {
                // 使用完整名称
                const_cast<EDA_NET_DATA*>(net)->updateDisplayNetname();
            }
        }
    }
    
    m_displayNetnamesDirty = false;
    if (m_displayNetnamesDirtyChangedCallback) {
        const_cast<EDA_NETINFO_LIST*>(this)->m_displayNetnamesDirtyChangedCallback(false);
    }
}

void EDA_NETINFO_LIST::appendNet(EDA_NET_DATA* newElement)
{
    if (!newElement || !validateNetItem(newElement)) {
        qWarning() << "EDA_NETINFO_LIST::appendNet: Invalid net item";
        return;
    }

    QMutexLocker locker(&m_mutex);

    // 检查是否已存在同名网络
    EDA_NET_DATA* sameName = getNetItem(newElement->getNetname());
    if (sameName) {
        // 如果存在同名网络，使用其网络代码
        newElement->setNetCode(sameName->getNetCode());
        return;
    }

    // 检查网络代码是否需要重新分配
    if (newElement->getNetCode() != static_cast<int>(m_netCodes.size()) || 
        newElement->getNetCode() < 0) {
        // 分配新的网络代码
        newElement->setNetCode(getFreeNetCode());
    }

    // 断言：网络名称和代码应该是唯一的
    Q_ASSERT(!getNetItem(newElement->getNetname()));
    Q_ASSERT(!getNetItem(newElement->getNetCode()));

    // 添加到映射表
    m_netNames.insert(newElement->getNetname(), newElement);
    m_netCodes.insert(newElement->getNetCode(), newElement);

    setDisplayNetnamesDirty(true);
    
    if (m_netAddedCallback) m_netAddedCallback(newElement);
    if (m_netCountChangedCallback) m_netCountChangedCallback(m_netNames.size());
}

void EDA_NETINFO_LIST::removeNet(EDA_NET_DATA* net)
{
    if (!net) {
        return;
    }

    QMutexLocker locker(&m_mutex);

    bool removed = false;

    // 从代码映射中删除
    auto codeIt = m_netCodes.find(net->getNetCode());
    if (codeIt != m_netCodes.end() && codeIt.value() == net) {
        m_netCodes.erase(codeIt);
        removed = true;
    }

    // 从名称映射中删除
    auto nameIt = m_netNames.find(net->getNetname());
    if (nameIt != m_netNames.end() && nameIt.value() == net) {
        Q_ASSERT_X(removed, "EDA_NETINFO_LIST::removeNet", 
                   "Target net found in m_netNames but not m_netCodes!");
        m_netNames.erase(nameIt);
    }

    if (removed) {
        // 更新新网络代码分配器
        m_newNetCode = std::min(m_newNetCode, net->getNetCode() - 1);
        setDisplayNetnamesDirty(true);

        if (m_netRemovedCallback) m_netRemovedCallback(net);
        if (m_netCountChangedCallback) m_netCountChangedCallback(m_netNames.size());
    }
}

void EDA_NETINFO_LIST::removeUnusedNets(QtBoardCommit* commit)
{
    QMutexLocker locker(&m_mutex);

    QtNetcodesMap existingNets = m_netCodes;
    QVector<EDA_NET_DATA*> unusedNets;

    m_netCodes.clear();
    m_netNames.clear();

    for (auto it = existingNets.begin(); it != existingNets.end(); ++it) {
        EDA_NET_DATA* netInfo = it.value();
        
        if (netInfo->isCurrent()) {
            m_netNames.insert(netInfo->getNetname(), netInfo);
            m_netCodes.insert(it.key(), netInfo);
        } else {
            setDisplayNetnamesDirty(true);
            
            if (commit) {
                commit->removed(netInfo);
            }
            
            unusedNets.append(netInfo);
        }
    }

    if (!unusedNets.isEmpty()) {
        if (m_netCountChangedCallback) m_netCountChangedCallback(m_netNames.size());
    }
}

void EDA_NETINFO_LIST::clear()
{
    QMutexLocker locker(&m_mutex);

    // 删除所有网络对象
    for (auto it = m_netNames.begin(); it != m_netNames.end(); ++it) {
        EDA_NET_DATA* net = it.value();
        delete net;
    }

    m_netNames.clear();
    m_netCodes.clear();
    m_newNetCode = 0;

    if (m_netsClearedCallback) m_netsClearedCallback();
    if (m_netCountChangedCallback) m_netCountChangedCallback(0);
}

void EDA_NETINFO_LIST::buildListOfNets()
{
    // 恢复NETINFO_ITEM的初始状态
    for (auto it = m_netNames.begin(); it != m_netNames.end(); ++it) {
        it.value()->clear();
    }

    // 临时实现：实际需要调用父板的同步方法
    if (m_parent) {
        // m_parent->synchronizeNetsAndNetClasses(false);
        // m_parent->setAreasNetCodesFromNetNames();
    }

    if (m_netsRebuiltCallback) m_netsRebuiltCallback();
}

void EDA_NETINFO_LIST::show() const
{
    QMutexLocker locker(&m_mutex);
    
    qDebug() << "EDA_NETINFO_LIST contents:";
    int i = 0;
    for (auto it = m_netNames.begin(); it != m_netNames.end(); ++it, ++i) {
        EDA_NET_DATA* net = it.value();
        qDebug() << QString("[%1]: netcode:%2  netname:<%3>")
                    .arg(i)
                    .arg(net->getNetCode())
                    .arg(net->getNetname());
    }
}

//==============================================================================
// EDA_NETINFO_LIST::Iterator Implementation
//==============================================================================

EDA_NETINFO_LIST::Iterator::Iterator(QtNetnamesMap::const_iterator iter)
    : m_iterator(iter)
{
}

const EDA_NETINFO_LIST::Iterator& EDA_NETINFO_LIST::Iterator::operator++()
{
    ++m_iterator;
    return *this;
}

EDA_NETINFO_LIST::Iterator EDA_NETINFO_LIST::Iterator::operator++(int)
{
    Iterator ret = *this;
    ++m_iterator;
    return ret;
}

EDA_NET_DATA* EDA_NETINFO_LIST::Iterator::operator*() const
{
    return m_iterator.value();
}

EDA_NET_DATA* EDA_NETINFO_LIST::Iterator::operator->() const
{
    return m_iterator.value();
}

bool EDA_NETINFO_LIST::Iterator::operator!=(const Iterator& other) const
{
    return m_iterator != other.m_iterator;
}

bool EDA_NETINFO_LIST::Iterator::operator==(const Iterator& other) const
{
    return m_iterator == other.m_iterator;
}

EDA_NETINFO_LIST::Iterator EDA_NETINFO_LIST::begin() const
{
    QMutexLocker locker(&m_mutex);
    return Iterator(m_netNames.begin());
}

EDA_NETINFO_LIST::Iterator EDA_NETINFO_LIST::end() const
{
    QMutexLocker locker(&m_mutex);
    return Iterator(m_netNames.end());
}

//==============================================================================
// Private Methods
//==============================================================================

int EDA_NETINFO_LIST::getFreeNetCode()
{
    do {
        if (m_newNetCode < 0) {
            m_newNetCode = 0;
        }
    } while (m_netCodes.contains(++m_newNetCode));

    return m_newNetCode;
}


QString EDA_NETINFO_LIST::unescapeString(const QString& str) const
{
    QString result = str;
    result.replace(QStringLiteral("\\n"), QStringLiteral("\n"));
    result.replace(QStringLiteral("\\t"), QStringLiteral("\t"));
    result.replace(QStringLiteral("\\\\"), QStringLiteral("\\"));
    return result;
}

bool EDA_NETINFO_LIST::validateNetItem(EDA_NET_DATA* netItem) const
{
    return netItem && netItem->getParent() == m_parent;
}

void EDA_NETINFO_LIST::ensureUnconnectedNet()
{
    // 确保未连接网络（代码0）存在
    if (!getNetItem(UNCONNECTED)) {
        EDA_NET_DATA* unconnectedNet = new EDA_NET_DATA(m_parent, QString(), UNCONNECTED);
        appendNet(unconnectedNet);
    }
}

void EDA_NETINFO_LIST::onNetItemChanged()
{
    // Callback handling for net item changes
    setDisplayNetnamesDirty(true);
}

void EDA_NETINFO_LIST::onNetItemDestroyed()
{
    // Callback handling for destroyed net items
    // Implementation depends on specific use case
}