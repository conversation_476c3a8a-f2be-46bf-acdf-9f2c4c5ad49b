#include "eda_zone_data.h"
#include "eda_board_object_container.h"
#include "eda_pad_data.h"
#include "eda_board_data.h"
#include "qt_temporary_implementations.h"

#include <QDebug>
#include <QtMath>
#include <algorithm>

// Qt enums to string conversion helpers
QString EDA_ZONE_DATA::fillModeToString(QtZoneFillMode mode)
{
    switch (mode) {
        case QtZoneFillMode::POLYGONS: return QStringLiteral("Polygons");
        case QtZoneFillMode::HATCH_PATTERN: return QStringLiteral("HatchPattern");
    }
    return QStringLiteral("Unknown");
}

QString EDA_ZONE_DATA::connectionToString(QtZoneConnection connection)
{
    switch (connection) {
        case QtZoneConnection::INHERIT: return QStringLiteral("Inherit");
        case QtZoneConnection::NONE: return QStringLiteral("None");
        case QtZoneConnection::THERMAL: return QStringLiteral("Thermal");
        case QtZoneConnection::FULL: return QStringLiteral("Full");
        case QtZoneConnection::THT_THERMAL: return QStringLiteral("ThtThermal");
    }
    return QStringLiteral("Unknown");
}

// Constructor
EDA_ZONE_DATA::EDA_ZONE_DATA(EDA_BOARD_OBJECT_CONTAINER* parent)
    : EDA_BOARD_CONNECTED_OBJECT(parent, QtKicadType::ZoneT),
      m_fillMode(QtZoneFillMode::POLYGONS),
      m_priority(0),
      m_cornerSmoothingType(0),
      m_cornerRadius(0),
      m_hatchOrientation(0.0),
      m_hatchGap(508000),  // 0.508mm
      m_hatchThickness(254000),  // 0.254mm
      m_hatchSmoothingLevel(0.0),
      m_hatchSmoothingValue(0.1),
      m_hatchBorderAlgorithm(0),
      m_hatchHoleMinArea(300000.0),
      m_teardropType(QtTeardropType::TD_NONE),
      m_thermalReliefGap(500000),  // 0.5mm
      m_thermalReliefSpokeWidth(500000),  // 0.5mm
      m_zoneMinThickness(254000),  // 0.254mm
      m_fillFlags(),
      m_isFilled(false),
      m_padConnection(QtZoneConnection::INHERITED),
      m_islandRemovalMode(QtIslandRemovalMode::ALWAYS),
      m_minIslandArea(10000000),  // 10mm²
      m_needRefill(false),
      m_fillVersion(6),
      m_doNotAllowCopperPour(false),
      m_doNotAllowVias(false),
      m_doNotAllowTracks(false),
      m_doNotAllowPads(false),
      m_doNotAllowFootprints(false),
      m_borderStyle(QtZoneBorderStyle::DIAGONAL_FULL),
      m_borderHatchPitch(508000)  // 0.508mm
{
    // Initialize default layerset for copper zone
    setLayer(QtPcbLayerId::FCu);
    
    // Initialize outline with a default empty polygon
    m_poly = QSharedPointer<QtShapePolySet>::create();
}

// Copy constructor
EDA_ZONE_DATA::EDA_ZONE_DATA(const EDA_ZONE_DATA& other)
    : EDA_BOARD_CONNECTED_OBJECT(other),
      m_fillMode(other.m_fillMode),
      m_priority(other.m_priority),
      m_cornerSmoothingType(other.m_cornerSmoothingType),
      m_cornerRadius(other.m_cornerRadius),
      m_hatchOrientation(other.m_hatchOrientation),
      m_hatchGap(other.m_hatchGap),
      m_hatchThickness(other.m_hatchThickness),
      m_hatchSmoothingLevel(other.m_hatchSmoothingLevel),
      m_hatchSmoothingValue(other.m_hatchSmoothingValue),
      m_hatchBorderAlgorithm(other.m_hatchBorderAlgorithm),
      m_hatchHoleMinArea(other.m_hatchHoleMinArea),
      m_teardropType(other.m_teardropType),
      m_thermalReliefGap(other.m_thermalReliefGap),
      m_thermalReliefSpokeWidth(other.m_thermalReliefSpokeWidth),
      m_zoneMinThickness(other.m_zoneMinThickness),
      m_fillFlags(other.m_fillFlags),
      m_isFilled(other.m_isFilled),
      m_padConnection(other.m_padConnection),
      m_islandRemovalMode(other.m_islandRemovalMode),
      m_minIslandArea(other.m_minIslandArea),
      m_needRefill(other.m_needRefill),
      m_fillVersion(other.m_fillVersion),
      m_doNotAllowCopperPour(other.m_doNotAllowCopperPour),
      m_doNotAllowVias(other.m_doNotAllowVias),
      m_doNotAllowTracks(other.m_doNotAllowTracks),
      m_doNotAllowPads(other.m_doNotAllowPads),
      m_doNotAllowFootprints(other.m_doNotAllowFootprints),
      m_borderStyle(other.m_borderStyle),
      m_borderHatchPitch(other.m_borderHatchPitch),
      m_zoneName(other.m_zoneName)
{
    // Deep copy outline
    m_poly = QSharedPointer<QtShapePolySet>::create(*other.m_poly);
    
    // Deep copy filled polygons
    for (auto it = other.m_filledPolysList.constBegin(); it != other.m_filledPolysList.constEnd(); ++it) {
        m_filledPolysList.insert(it.key(), QSharedPointer<QtShapePolySet>::create(*it.value()));
    }
}

// Destructor
EDA_ZONE_DATA::~EDA_ZONE_DATA() = default;

// Assignment operator
EDA_ZONE_DATA& EDA_ZONE_DATA::operator=(const EDA_ZONE_DATA& other)
{
    if (this != &other) {
        EDA_BOARD_CONNECTED_OBJECT::operator=(other);
        
        m_fillMode = other.m_fillMode;
        m_priority = other.m_priority;
        m_cornerSmoothingType = other.m_cornerSmoothingType;
        m_cornerRadius = other.m_cornerRadius;
        m_hatchOrientation = other.m_hatchOrientation;
        m_hatchGap = other.m_hatchGap;
        m_hatchThickness = other.m_hatchThickness;
        m_hatchSmoothingLevel = other.m_hatchSmoothingLevel;
        m_hatchSmoothingValue = other.m_hatchSmoothingValue;
        m_hatchBorderAlgorithm = other.m_hatchBorderAlgorithm;
        m_hatchHoleMinArea = other.m_hatchHoleMinArea;
        m_teardropType = other.m_teardropType;
        m_thermalReliefGap = other.m_thermalReliefGap;
        m_thermalReliefSpokeWidth = other.m_thermalReliefSpokeWidth;
        m_zoneMinThickness = other.m_zoneMinThickness;
        m_fillFlags = other.m_fillFlags;
        m_isFilled = other.m_isFilled;
        m_padConnection = other.m_padConnection;
        m_islandRemovalMode = other.m_islandRemovalMode;
        m_minIslandArea = other.m_minIslandArea;
        m_needRefill = other.m_needRefill;
        m_fillVersion = other.m_fillVersion;
        m_doNotAllowCopperPour = other.m_doNotAllowCopperPour;
        m_doNotAllowVias = other.m_doNotAllowVias;
        m_doNotAllowTracks = other.m_doNotAllowTracks;
        m_doNotAllowPads = other.m_doNotAllowPads;
        m_doNotAllowFootprints = other.m_doNotAllowFootprints;
        m_borderStyle = other.m_borderStyle;
        m_borderHatchPitch = other.m_borderHatchPitch;
        m_zoneName = other.m_zoneName;
        
        // Deep copy outline
        m_poly = QSharedPointer<QtShapePolySet>::create(*other.m_poly);
        
        // Deep copy filled polygons
        m_filledPolysList.clear();
        for (auto it = other.m_filledPolysList.constBegin(); it != other.m_filledPolysList.constEnd(); ++it) {
            m_filledPolysList.insert(it.key(), QSharedPointer<QtShapePolySet>::create(*it.value()));
        }
    }
    return *this;
}

// Clone
EDA_OBJECT_DATA* EDA_ZONE_DATA::clone() const
{
    return new EDA_ZONE_DATA(*this);
}

// Position and transformation
QPointF EDA_ZONE_DATA::getPosition() const
{
    return m_poly->bbox().center();
}

void EDA_ZONE_DATA::setPosition(const QPointF& pos)
{
    QPointF delta = pos - getPosition();
    move(delta);
}

void EDA_ZONE_DATA::move(const QPointF& offset)
{
    m_poly->move(offset);
    
    // Move filled polygons
    for (auto& poly : m_filledPolysList) {
        poly->move(offset);
    }
    
    // Position changed
}

void EDA_ZONE_DATA::rotate(const QPointF& center, const QtEdaAngle& angle)
{
    double radians = angle.asRadians();
    
    // Rotate outline
    m_poly->rotate(radians, center);
    
    // Rotate filled polygons
    for (auto& poly : m_filledPolysList) {
        poly->rotate(radians, center);
    }
    
    // Position changed
}

void EDA_ZONE_DATA::flip(const QPointF& center, QtFlipDirection direction)
{
    // Mirror outline
    mirrorPolygon(*m_poly, center, direction);
    
    // Mirror filled polygons
    for (auto& poly : m_filledPolysList) {
        mirrorPolygon(*poly, center, direction);
    }
    
    // Flip layer
    QtPcbLayerId newLayer = flipLayer(getLayer());
    setLayer(newLayer);
    
    // Layer changed
    // Position changed
}

// Geometry
QRectF EDA_ZONE_DATA::getBoundingBox() const
{
    return m_poly->bbox();
}

double EDA_ZONE_DATA::getArea(QtPcbLayerId layer) const
{
    auto it = m_filledPolysList.find(layer);
    if (it != m_filledPolysList.end()) {
        return it.value()->area();
    }
    return 0.0;
}

bool EDA_ZONE_DATA::isEmpty() const
{
    return m_poly->isEmpty();
}

// Outline management
void EDA_ZONE_DATA::setOutline(QtShapePolySet* outline)
{
    m_poly = QSharedPointer<QtShapePolySet>(outline);
    setNeedRefill(true);
    // Outline changed
}

// Filled polygons management
const QSharedPointer<QtShapePolySet>& EDA_ZONE_DATA::getFilledPolysList(QtPcbLayerId layer) const
{
    static QSharedPointer<QtShapePolySet> empty;
    auto it = m_filledPolysList.find(layer);
    if (it != m_filledPolysList.end()) {
        return it.value();
    }
    return empty;
}

QtShapePolySet* EDA_ZONE_DATA::getFill(QtPcbLayerId layer)
{
    auto it = m_filledPolysList.find(layer);
    if (it != m_filledPolysList.end()) {
        return it.value().data();
    }
    
    // Create new polygon set for this layer
    auto polySet = QSharedPointer<QtShapePolySet>::create();
    m_filledPolysList.insert(layer, polySet);
    return polySet.data();
}

void EDA_ZONE_DATA::setFilledPolysList(QtPcbLayerId layer, const QtShapePolySet& polys)
{
    m_filledPolysList[layer] = QSharedPointer<QtShapePolySet>::create(polys);
    setIsFilled(true);
    // Filled polygons changed
}

// Hit testing
bool EDA_ZONE_DATA::hitTest(const QPointF& position, int accuracy) const
{
    return hitTestForCorner(position, accuracy) || hitTestForEdge(position, accuracy);
}

bool EDA_ZONE_DATA::hitTestForCorner(const QPointF& position, int accuracy,
                        QtShapePolySet::VertexIndex* cornerHit) const
{
    return m_poly->collideVertex(position, cornerHit, accuracy);
}

bool EDA_ZONE_DATA::hitTestForEdge(const QPointF& position, int accuracy,
                      QtShapePolySet::VertexIndex* cornerHit) const
{
    return m_poly->collideEdge(position, cornerHit, accuracy);
}

bool EDA_ZONE_DATA::hitTestFilledArea(QtPcbLayerId layer, const QPointF& refPos, int accuracy) const
{
    auto it = m_filledPolysList.find(layer);
    if (it != m_filledPolysList.end()) {
        return it.value()->collide(refPos, accuracy);
    }
    return false;
}

// Utility functions
void EDA_ZONE_DATA::mirrorPolygon(QtShapePolySet& poly, const QPointF& ref, QtFlipDirection direction)
{
    // Implementation would mirror the polygon
    // For now, just a placeholder
    Q_UNUSED(poly)
    Q_UNUSED(ref)
    Q_UNUSED(direction)
}

QtPcbLayerId EDA_ZONE_DATA::flipLayer(QtPcbLayerId layer) const
{
    // Simple front/back flip
    if (layer == QtPcbLayerId::FCu) return QtPcbLayerId::BCu;
    if (layer == QtPcbLayerId::BCu) return QtPcbLayerId::FCu;
    
    // Add more layer mappings as needed
    return layer;
}

// Message panel info
void EDA_ZONE_DATA::getMsgPanelInfo(QtEdaDrawFrame* frame, QVector<QtMsgPanelItem>& list) const
{
    EDA_BOARD_CONNECTED_OBJECT::getMsgPanelInfo(frame, list);
    
    list.append(QtMsgPanelItem("Zone Name", m_zoneName));
    list.append(QtMsgPanelItem("Priority", QString::number(m_priority)));
    list.append(QtMsgPanelItem("Fill Mode", fillModeToString(m_fillMode)));
    list.append(QtMsgPanelItem("Connection", connectionToString(m_padConnection)));
    list.append(QtMsgPanelItem("Filled", m_isFilled ? "Yes" : "No"));
    
    // Add thermal relief info
    if (m_padConnection == QtZoneConnection::THT_THERMAL) {
        list.append(QtMsgPanelItem("Thermal Gap", 
            frame->messageTextFromValue(m_thermalReliefGap)));
        list.append(QtMsgPanelItem("Thermal Width", 
            frame->messageTextFromValue(m_thermalReliefSpokeWidth)));
    }
}

//=============================================================================
// PURE VIRTUAL METHOD IMPLEMENTATIONS
//=============================================================================

double EDA_ZONE_DATA::similarity(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return 0.0;
    }
    
    const EDA_ZONE_DATA* zone = dynamic_cast<const EDA_ZONE_DATA*>(&other);
    if (!zone) {
        return 0.0;
    }
    
    // Compare basic properties
    double score = 0.0;
    
    // Layer set comparison (40% weight)
    if (getLayerSet() == zone->getLayerSet()) {
        score += 0.4;
    }
    
    // Net comparison (30% weight)
    if (getNetCode() == zone->getNetCode()) {
        score += 0.3;
    }
    
    // Priority comparison (10% weight)
    if (m_priority == zone->m_priority) {
        score += 0.1;
    }
    
    // Fill mode comparison (10% weight)
    if (m_fillMode == zone->m_fillMode) {
        score += 0.1;
    }
    
    // Zone name comparison (10% weight)
    if (m_zoneName == zone->m_zoneName) {
        score += 0.1;
    }
    
    return score;
}

bool EDA_ZONE_DATA::operator==(const EDA_BOARD_OBJECT_DATA& other) const
{
    if (other.getType() != getType()) {
        return false;
    }
    
    const EDA_ZONE_DATA* zone = dynamic_cast<const EDA_ZONE_DATA*>(&other);
    if (!zone) {
        return false;
    }
    
    return operator==(*zone);
}

// Additional missing methods
QString EDA_ZONE_DATA::getItemDescription(QtUnitsProvider* unitsProvider, bool full) const
{
    Q_UNUSED(unitsProvider)
    Q_UNUSED(full)
    return QString("Zone %1").arg(m_zoneName.isEmpty() ? "Unnamed" : m_zoneName);
}

QtBitmaps EDA_ZONE_DATA::getMenuImage() const
{
    return QtBitmaps::DummyItem;
}

QString EDA_ZONE_DATA::getFriendlyName() const
{
    return QString("Zone %1").arg(m_zoneName.isEmpty() ? "Unnamed" : m_zoneName);
}

bool EDA_ZONE_DATA::classOf(const EDA_OBJECT_DATA* item)
{
    return item && item->getType() == QtKicadType::PcbZoneT;
}

QByteArray EDA_ZONE_DATA::serialize() const
{
    // Placeholder implementation
    return QByteArray();
}

bool EDA_ZONE_DATA::deserialize(const QByteArray& data)
{
    Q_UNUSED(data)
    // Placeholder implementation
    return false;
}

bool EDA_ZONE_DATA::isConnected() const
{
    return getNetCode() > 0;
}

void EDA_ZONE_DATA::setIsFilled(bool filled)
{
    m_isFilled = filled;
}

void EDA_ZONE_DATA::setNeedRefill(bool needRefill)
{
    m_needRefill = needRefill;
}

void EDA_ZONE_DATA::setPadConnection(QtZoneConnection connection)
{
    m_padConnection = connection;
}

// Layer methods
bool EDA_ZONE_DATA::isOnCopperLayer() const
{
    return true; // Simplified - zones are typically on copper layers
}

QtPcbLayerId EDA_ZONE_DATA::getLayer() const
{
    // Return the first layer from the layer set
    auto layers = m_layerSet.sequence();
    return layers.isEmpty() ? QtPcbLayerId::FCu : layers.first();
}

void EDA_ZONE_DATA::setLayer(QtPcbLayerId layer)
{
    m_layerSet = QtLayerSet(layer);
}

bool EDA_ZONE_DATA::isOnLayer(QtPcbLayerId layer) const
{
    return m_layerSet.test(layer);
}

void EDA_ZONE_DATA::setLayerSet(const QtLayerSet& layerSet)
{
    m_layerSet = layerSet;
}

double EDA_ZONE_DATA::viewGetLOD(int layer, const QtView* view) const
{
    Q_UNUSED(layer)
    Q_UNUSED(view)
    return 1.0; // Default LOD
}

// Missing hit test method
bool EDA_ZONE_DATA::hitTestCutout(const QPointF& refPos, int* outlineIdx, int* holeIdx) const
{
    Q_UNUSED(refPos)
    Q_UNUSED(outlineIdx) 
    Q_UNUSED(holeIdx)
    return false; // Placeholder
}

// Missing hit test with rect
bool EDA_ZONE_DATA::hitTest(const QRectF& rect, bool contained, int accuracy) const
{
    Q_UNUSED(rect)
    Q_UNUSED(contained)
    Q_UNUSED(accuracy)
    return false; // Placeholder
}

// Missing swap data method
void EDA_ZONE_DATA::swapData(EDA_BOARD_OBJECT_DATA* image)
{
    Q_UNUSED(image)
    // Placeholder implementation
}

// Add missing methods for priority and zone name
void EDA_ZONE_DATA::setAssignedPriority(unsigned priority)
{
    m_priority = priority;
}

void EDA_ZONE_DATA::setZoneName(const QString& name)
{
    m_zoneName = name;
}

bool EDA_ZONE_DATA::higherPriority(const EDA_ZONE_DATA* other) const
{
    return other ? m_priority > other->m_priority : true;
}

bool EDA_ZONE_DATA::sameNet(const EDA_ZONE_DATA* other) const
{
    return other ? getNetCode() == other->getNetCode() : false;
}

bool EDA_ZONE_DATA::operator==(const EDA_ZONE_DATA& other) const
{
    // Compare all significant properties
    return EDA_BOARD_CONNECTED_OBJECT::operator==(other) &&
           m_layerSet == other.m_layerSet &&
           m_priority == other.m_priority &&
           m_fillMode == other.m_fillMode &&
           m_zoneName == other.m_zoneName &&
           m_thermalReliefGap == other.m_thermalReliefGap &&
           m_thermalReliefSpokeWidth == other.m_thermalReliefSpokeWidth &&
           m_zoneMinThickness == other.m_zoneMinThickness &&
           m_padConnection == other.m_padConnection &&
           m_isRuleArea == other.m_isRuleArea &&
           m_teardropType == other.m_teardropType;
}
