/*
 * Qt-based reimplementation of KiCad PADSTACK class
 * 
 * This class defines the characteristics of a single or multi-layer pad
 * or via stack, providing layer-specific geometry, drilling, thermal relief,
 * clearance settings, and custom shape support using Qt frameworks.
 */

#pragma once

#include "eda_board_object_data.h"
#include <QtCore/QString>
#include <QtCore/QVector>
#include <QtCore/QPointF>
#include <QtCore/QSizeF>
#include <QtCore/QMap>
#include <QtCore/QHash>
#include <QtCore/QList>
#include <QtCore/QSharedPointer>
#include <QtCore/QMetaEnum>
#include <QtCore/QLoggingCategory>
#include <QtCore/QVariantMap>
#include <QtCore/QUuid>
#include <optional>
#include <functional>
#include <memory>

// Logging category removed (Qt object system removal)

// Forward declarations for dependencies not in migration scope
class EDA_BOARD_OBJECT_DATA;
class EDA_BRD_SHAPE_DATA;

/**
 * @brief Qt-based pad shape enumeration
 */
enum class QtPadShape : int {
    Circle = 0,
    Rectangle = 1,
    Oval = 2,
    Trapezoid = 3,
    RoundRect = 4,
    ChamferedRect = 5,
    Custom = 6
};
// Qt enum registration removed

/**
 * @brief Qt-based pad drill shape enumeration
 */
enum class QtPadDrillShape : int {
    Undefined = 0,
    Circle = 1,
    Oblong = 2,
    Oval = 3,
};
// Qt enum registration removed

/**
 * @brief Qt-based zone connection modes
 */
enum class QtZoneConnection : int {
    Inherited = -1,  ///< Use parent/rule settings
    None = 0,        ///< No connection to zone
    Thermal = 1,     ///< Thermal relief connection
    Full = 2         ///< Full connection to zone
};
// Qt enum registration removed

/**
 * @brief Qt-based padstack type
 */
enum class QtPadStackType : int {
    Normal = 0,      ///< Padstack for a footprint pad
    Via = 1,         ///< Padstack for a via
    Mounting = 2     ///< Mounting hole (plated or unplated)
};
// Qt enum registration removed

/**
 * @brief Qt-based padstack copper geometry mode
 */
enum class QtPadStackMode : int {
    Normal = 0,           ///< Same shape on all layers
    FrontInnerBack = 1,   ///< Up to three shapes (F_Cu, inner, B_Cu)
    Custom = 2            ///< Arbitrary layer-specific shapes
};
// Qt enum registration removed

/**
 * @brief Qt-based unconnected layer handling mode
 */
enum class QtUnconnectedLayerMode : int {
    KeepAll = 0,                  ///< Keep copper on all layers
    RemoveAll = 1,                ///< Remove copper on unconnected layers
    RemoveExceptStartAndEnd = 2   ///< Keep start/end layers, remove others
};
// Qt enum registration removed

/**
 * @brief Qt-based custom shape zone mode
 */
enum class QtCustomShapeZoneMode : int {
    Outline = 0,      ///< Use pad outline for zone clearance
    ConvexHull = 1    ///< Use convex hull for zone clearance
};
// Qt enum registration removed

/**
 * @brief Chamfer positions for chamfered rectangles
 */
enum QtChamferPosition : int {
    ChamferTopLeft = 1,
    ChamferTopRight = 2,
    ChamferBottomLeft = 4,
    ChamferBottomRight = 8,
    ChamferAll = 15
};
// Qt flags system removed - use standard int operations

/**
 * @brief Qt-based shape properties for a padstack layer
 */
struct QtPadStackShapeProperties {
    QtPadShape shape = QtPadShape::Circle;
    QtPadShape anchorShape = QtPadShape::Circle;  ///< For custom shapes
    QSizeF size = QSizeF(1.0, 1.0);              ///< Shape size
    QPointF offset = QPointF(0.0, 0.0);          ///< Offset from pad center
    
    // Round rectangle properties
    double roundRectCornerRadius = 0.0;
    double roundRectRadiusRatio = 0.25;
    
    // Chamfered rectangle properties
    double chamferRatio = 0.2;
    int chamferPositions = QtChamferPosition::ChamferAll;
    
    // Trapezoid properties
    QSizeF trapezoidDelta = QSizeF(0.0, 0.0);
    
    QtPadStackShapeProperties() = default;
    
    bool operator==(const QtPadStackShapeProperties& other) const {
        return shape == other.shape &&
               anchorShape == other.anchorShape &&
               qFuzzyCompare(size.width(), other.size.width()) &&
               qFuzzyCompare(size.height(), other.size.height()) &&
               qFuzzyCompare(offset.x(), other.offset.x()) &&
               qFuzzyCompare(offset.y(), other.offset.y()) &&
               qFuzzyCompare(roundRectCornerRadius, other.roundRectCornerRadius) &&
               qFuzzyCompare(roundRectRadiusRatio, other.roundRectRadiusRatio) &&
               qFuzzyCompare(chamferRatio, other.chamferRatio) &&
               chamferPositions == other.chamferPositions &&
               qFuzzyCompare(trapezoidDelta.width(), other.trapezoidDelta.width()) &&
               qFuzzyCompare(trapezoidDelta.height(), other.trapezoidDelta.height());
    }
    
    bool operator!=(const QtPadStackShapeProperties& other) const {
        return !(*this == other);
    }
};

/**
 * @brief Qt-based copper layer properties
 */
struct QtCopperLayerProperties {
    QtPadStackShapeProperties shape;  ///< Shape properties for this layer
    
    // Zone connection and thermal properties
    std::optional<QtZoneConnection> zoneConnection;
    std::optional<int> thermalSpokeWidth;
    std::optional<double> thermalSpokeAngle;  ///< In degrees
    std::optional<int> thermalGap;
    std::optional<int> clearance;
    
    // Custom shapes for this layer
    QList<QSharedPointer<EDA_BRD_SHAPE_DATA>> customShapes;
    
    QtCopperLayerProperties() = default;
    
    bool operator==(const QtCopperLayerProperties& other) const {
        return shape == other.shape &&
               zoneConnection == other.zoneConnection &&
               thermalSpokeWidth == other.thermalSpokeWidth &&
               thermalSpokeAngle == other.thermalSpokeAngle &&
               thermalGap == other.thermalGap &&
               clearance == other.clearance;
               // Note: We skip customShapes comparison for performance
    }
    
    bool operator!=(const QtCopperLayerProperties& other) const {
        return !(*this == other);
    }
};

/**
 * @brief Qt-based mask layer properties (solder mask and paste)
 */
struct QtMaskLayerProperties {
    std::optional<int> solderMaskMargin;
    std::optional<int> solderPasteMargin;
    std::optional<double> solderPasteMarginRatio;
    std::optional<bool> hasSolderMask;    ///< True if layer has mask (not tented)
    std::optional<bool> hasSolderPaste;   ///< True if layer has solder paste
    
    QtMaskLayerProperties() = default;
    
    bool operator==(const QtMaskLayerProperties& other) const {
        return solderMaskMargin == other.solderMaskMargin &&
               solderPasteMargin == other.solderPasteMargin &&
               solderPasteMarginRatio == other.solderPasteMarginRatio &&
               hasSolderMask == other.hasSolderMask &&
               hasSolderPaste == other.hasSolderPaste;
    }
    
    bool operator!=(const QtMaskLayerProperties& other) const {
        return !(*this == other);
    }
};

/**
 * @brief Qt-based drill properties
 */
struct QtDrillProperties {
    QSizeF size = QSizeF(0.0, 0.0);      ///< Drill diameter or slot dimensions
    QtPadDrillShape shape = QtPadDrillShape::Circle;
    QtPcbLayerId startLayer = QtPcbLayerId::FCu;
    QtPcbLayerId endLayer = QtPcbLayerId::BCu;
    
    QtDrillProperties() = default;
    QtDrillProperties(const QSizeF& drillSize, QtPadDrillShape drillShape = QtPadDrillShape::Circle)
        : size(drillSize), shape(drillShape) {}
    
    bool hasHole() const { 
        return size.width() > 0.0 && size.height() > 0.0; 
    }
    
    bool isCircular() const { 
        return shape == QtPadDrillShape::Circle || qFuzzyCompare(size.width(), size.height()); 
    }
    
    bool operator==(const QtDrillProperties& other) const {
        return qFuzzyCompare(size.width(), other.size.width()) &&
               qFuzzyCompare(size.height(), other.size.height()) &&
               shape == other.shape &&
               startLayer == other.startLayer &&
               endLayer == other.endLayer;
    }
    
    bool operator!=(const QtDrillProperties& other) const {
        return !(*this == other);
    }
};

/**
 * @brief Qt-based reimplementation of KiCad's PADSTACK class
 * 
 * This class defines the characteristics of a single or multi-layer pad
 * or via, providing comprehensive layer management, shape definition,
 * drilling support, thermal relief, and clearance settings using Qt frameworks.
 */
class EDA_PADSTACK_DATA
{
public:
    ///! Temporary layer identifier for all-layers operations
    static constexpr QtPcbLayerId ALL_LAYERS = QtPcbLayerId::FCu;
    
    ///! Layer identifier for inner layers in front/inner/back mode
    static constexpr QtPcbLayerId INNER_LAYERS = QtPcbLayerId::In1Cu;

    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_PADSTACK_DATA(EDA_BOARD_OBJECT_DATA* parent = nullptr);
    EDA_PADSTACK_DATA(const EDA_PADSTACK_DATA& other);
    ~EDA_PADSTACK_DATA();

    // Assignment operator
    EDA_PADSTACK_DATA& operator=(const EDA_PADSTACK_DATA& other);

    //==========================================================================
    // COMPARISON AND SIMILARITY
    //==========================================================================
    bool operator==(const EDA_PADSTACK_DATA& other) const;
    bool operator!=(const EDA_PADSTACK_DATA& other) const { return !(*this == other); }
    
    double similarity(const EDA_PADSTACK_DATA& other) const;
    
    static int compare(const EDA_PADSTACK_DATA* stackRef, const EDA_PADSTACK_DATA* stackCmp);

    //==========================================================================
    // BASIC PROPERTIES
    //==========================================================================
    QtPadStackMode mode() const { return m_mode; }
    void setMode(QtPadStackMode mode);
    
    QtPadStackType type() const { return m_type; }
    void setType(QtPadStackType type) { m_type = type; }
    
    QtLayerSet layerSet() const { return m_layerSet; }
    void setLayerSet(const QtLayerSet& layerSet);
    
    QString customName() const { return m_customName; }
    void setCustomName(const QString& name);
    
    QString name() const;  ///< IPC-7351 format name
    
    double orientation() const { return m_orientation; }
    void setOrientation(double degrees);
    
    //==========================================================================
    // LAYER MANAGEMENT
    //==========================================================================
    QtPcbLayerId startLayer() const;
    QtPcbLayerId endLayer() const;
    
    void flipLayers(int copperLayerCount);
    
    /**
     * @brief Get the effective layer for storage based on padstack mode
     * @param layer Input layer
     * @return The layer where properties are actually stored
     */
    QtPcbLayerId effectiveLayerFor(QtPcbLayerId layer) const;
    
    /**
     * @brief Get unique layers that define this padstack's geometry
     * @return List of layers that must be considered for geometry
     */
    QList<QtPcbLayerId> uniqueLayers() const;
    
    /**
     * @brief Run callback for each unique copper layer in this padstack
     * @param method Callback to run for each layer
     */
    void forEachUniqueLayer(const std::function<void(QtPcbLayerId)>& method) const;
    
    /**
     * @brief Get layers that must be checked when comparing with another padstack
     * @param other Other padstack to compare with
     * @return Set of relevant layers for comparison
     */
    QtLayerSet relevantShapeLayers(const EDA_PADSTACK_DATA& other) const;

    //==========================================================================
    // DRILL PROPERTIES
    //==========================================================================
    QtDrillProperties& drill() { return m_drill; }
    const QtDrillProperties& drill() const { return m_drill; }
    void setDrill(const QtDrillProperties& drill);
    
    QtDrillProperties& secondaryDrill() { return m_secondaryDrill; }
    const QtDrillProperties& secondaryDrill() const { return m_secondaryDrill; }
    void setSecondaryDrill(const QtDrillProperties& drill);
    
    bool hasPrimaryDrill() const { return m_drill.hasHole(); }
    bool hasSecondaryDrill() const { return m_secondaryDrill.hasHole(); }
    
    QtPadDrillShape drillShape() const { return m_drill.shape; }
    void setDrillShape(QtPadDrillShape shape);

    //==========================================================================
    // UNCONNECTED LAYER HANDLING
    //==========================================================================
    QtUnconnectedLayerMode unconnectedLayerMode() const { return m_unconnectedLayerMode; }
    void setUnconnectedLayerMode(QtUnconnectedLayerMode mode);

    //==========================================================================
    // CUSTOM SHAPE ZONE MODE
    //==========================================================================
    QtCustomShapeZoneMode customShapeZoneMode() const { return m_customShapeZoneMode; }
    void setCustomShapeZoneMode(QtCustomShapeZoneMode mode);

    //==========================================================================
    // COPPER LAYER ACCESS
    //==========================================================================
    QtCopperLayerProperties& copperLayer(QtPcbLayerId layer);
    const QtCopperLayerProperties& copperLayer(QtPcbLayerId layer) const;
    
    //==========================================================================
    // MASK LAYER ACCESS
    //==========================================================================
    QtMaskLayerProperties& frontOuterLayers() { return m_frontMaskProps; }
    const QtMaskLayerProperties& frontOuterLayers() const { return m_frontMaskProps; }
    
    QtMaskLayerProperties& backOuterLayers() { return m_backMaskProps; }
    const QtMaskLayerProperties& backOuterLayers() const { return m_backMaskProps; }
    
    /**
     * @brief Check if padstack is tented (covered in soldermask) on given side
     * @param side Front or back layer
     * @return True/false if override is set, nullopt if no override (use design rules)
     */
    std::optional<bool> isTented(QtPcbLayerId side) const;

    //==========================================================================
    // SHAPE PROPERTIES CONVENIENCE METHODS
    //==========================================================================
    
    // Shape access
    QtPadShape shape(QtPcbLayerId layer = ALL_LAYERS) const;
    void setShape(QtPadShape shape, QtPcbLayerId layer = ALL_LAYERS);
    
    // Size access
    QSizeF size(QtPcbLayerId layer = ALL_LAYERS) const;
    void setSize(const QSizeF& size, QtPcbLayerId layer = ALL_LAYERS);
    
    // Offset access
    QPointF offset(QtPcbLayerId layer = ALL_LAYERS) const;
    void setOffset(const QPointF& offset, QtPcbLayerId layer = ALL_LAYERS);
    
    // Anchor shape for custom pads
    QtPadShape anchorShape(QtPcbLayerId layer = ALL_LAYERS) const;
    void setAnchorShape(QtPadShape shape, QtPcbLayerId layer = ALL_LAYERS);
    
    // Trapezoid delta
    QSizeF trapezoidDeltaSize(QtPcbLayerId layer = ALL_LAYERS) const;
    void setTrapezoidDeltaSize(const QSizeF& delta, QtPcbLayerId layer = ALL_LAYERS);
    
    // Round rectangle properties
    double roundRectRadiusRatio(QtPcbLayerId layer = ALL_LAYERS) const;
    void setRoundRectRadiusRatio(double ratio, QtPcbLayerId layer = ALL_LAYERS);
    
    double roundRectRadius(QtPcbLayerId layer = ALL_LAYERS) const;
    void setRoundRectRadius(double radius, QtPcbLayerId layer = ALL_LAYERS);
    
    // Chamfered rectangle properties
    double chamferRatio(QtPcbLayerId layer = ALL_LAYERS) const;
    void setChamferRatio(double ratio, QtPcbLayerId layer = ALL_LAYERS);
    
    int chamferPositions(QtPcbLayerId layer = ALL_LAYERS) const;
    void setChamferPositions(int positions, QtPcbLayerId layer = ALL_LAYERS);

    //==========================================================================
    // ELECTRICAL PROPERTIES CONVENIENCE METHODS
    //==========================================================================
    
    // Clearance
    std::optional<int> clearance(QtPcbLayerId layer = ALL_LAYERS) const;
    void setClearance(std::optional<int> clearance, QtPcbLayerId layer = ALL_LAYERS);
    
    // Solder mask
    std::optional<int> solderMaskMargin(QtPcbLayerId layer = ALL_LAYERS) const;
    void setSolderMaskMargin(std::optional<int> margin, QtPcbLayerId layer = ALL_LAYERS);
    
    // Solder paste
    std::optional<int> solderPasteMargin(QtPcbLayerId layer = ALL_LAYERS) const;
    void setSolderPasteMargin(std::optional<int> margin, QtPcbLayerId layer = ALL_LAYERS);
    
    std::optional<double> solderPasteMarginRatio(QtPcbLayerId layer = ALL_LAYERS) const;
    void setSolderPasteMarginRatio(std::optional<double> ratio, QtPcbLayerId layer = ALL_LAYERS);
    
    // Zone connection
    std::optional<QtZoneConnection> zoneConnection(QtPcbLayerId layer = ALL_LAYERS) const;
    void setZoneConnection(std::optional<QtZoneConnection> connection, QtPcbLayerId layer = ALL_LAYERS);
    
    // Thermal properties
    std::optional<int> thermalSpokeWidth(QtPcbLayerId layer = ALL_LAYERS) const;
    void setThermalSpokeWidth(std::optional<int> width, QtPcbLayerId layer = ALL_LAYERS);
    
    std::optional<int> thermalGap(QtPcbLayerId layer = ALL_LAYERS) const;
    void setThermalGap(std::optional<int> gap, QtPcbLayerId layer = ALL_LAYERS);
    
    double thermalSpokeAngle(QtPcbLayerId layer = ALL_LAYERS) const;
    void setThermalSpokeAngle(double degrees, QtPcbLayerId layer = ALL_LAYERS);
    
    double defaultThermalSpokeAngleForShape(QtPcbLayerId layer = ALL_LAYERS) const;

    //==========================================================================
    // CUSTOM SHAPE PRIMITIVES
    //==========================================================================
    QList<QSharedPointer<EDA_BRD_SHAPE_DATA>>& primitives(QtPcbLayerId layer);
    const QList<QSharedPointer<EDA_BRD_SHAPE_DATA>>& primitives(QtPcbLayerId layer) const;
    
    void addPrimitive(QSharedPointer<EDA_BRD_SHAPE_DATA> shape, QtPcbLayerId layer);
    void appendPrimitives(const QList<QSharedPointer<EDA_BRD_SHAPE_DATA>>& primitives, QtPcbLayerId layer);
    void replacePrimitives(const QList<QSharedPointer<EDA_BRD_SHAPE_DATA>>& primitives, QtPcbLayerId layer);
    void clearPrimitives(QtPcbLayerId layer);

    //==========================================================================
    // QT SERIALIZATION
    //==========================================================================
    QVariantMap toVariantMap() const;
    void fromVariantMap(const QVariantMap& map);
    
    QString toString() const;

private:
    //==========================================================================
    // PRIVATE HELPER METHODS
    //==========================================================================
    void ensureCopperLayer(QtPcbLayerId layer);
    void deepCopyCustomShapes(const EDA_PADSTACK_DATA& other);
    void initializeDefaultProperties();

private:
    //==========================================================================
    // PRIVATE DATA MEMBERS
    //==========================================================================
    
    ///! The BOARD_ITEM this padstack belongs to
    EDA_BOARD_OBJECT_DATA* m_parent;
    
    ///! Padstack type (normal pad, via, mounting hole)
    QtPadStackType m_type;
    
    ///! Copper layer variation mode
    QtPadStackMode m_mode;
    
    ///! Board layers this padstack is active on
    QtLayerSet m_layerSet;
    
    ///! IPC-7351 padstack name override
    QString m_customName;
    
    ///! Rotation relative to outer reference frame
    double m_orientation;  // in degrees
    
    ///! Layer-specific copper properties
    QHash<QtPcbLayerId, QtCopperLayerProperties> m_copperProps;
    
    ///! Front outer technical layer properties
    QtMaskLayerProperties m_frontMaskProps;
    
    ///! Back outer technical layer properties  
    QtMaskLayerProperties m_backMaskProps;
    
    ///! How to handle unconnected layers
    QtUnconnectedLayerMode m_unconnectedLayerMode;
    
    ///! How to build custom shape clearance areas in zones
    QtCustomShapeZoneMode m_customShapeZoneMode;
    
    ///! Primary drill parameters
    QtDrillProperties m_drill;
    
    ///! Secondary drill for back-drilling
    QtDrillProperties m_secondaryDrill;
};

//=============================================================================
// UTILITY FUNCTIONS AND TYPE ALIASES
//=============================================================================

// Container type aliases
using QtPadStacks = QList<EDA_PADSTACK_DATA*>;
using QtPadStackList = QVector<EDA_PADSTACK_DATA*>;

// Hash support for padstack enums
inline uint qHash(QtPadShape shape, uint seed = 0) {
    return static_cast<uint>(qHash(static_cast<int>(shape), seed));
}

inline uint qHash(QtPadDrillShape shape, uint seed = 0) {
    return static_cast<uint>(qHash(static_cast<int>(shape), seed));
}

inline uint qHash(QtZoneConnection connection, uint seed = 0) {
    return static_cast<uint>(qHash(static_cast<int>(connection), seed));
}

inline uint qHash(QtPadStackMode mode, uint seed = 0) {
    return static_cast<uint>(qHash(static_cast<int>(mode), seed));
}

// Utility namespace for padstack operations
namespace QtPadStackUtils {
    /**
     * @brief Get canonical name for pad shape
     */
    QString getShapeCanonicalName(QtPadShape shape);
    
    /**
     * @brief Get pad shape from canonical name 
     */
    QtPadShape getShapeFromCanonicalName(const QString& name);
    
    /**
     * @brief Get canonical name for drill shape
     */
    QString getDrillShapeCanonicalName(QtPadDrillShape shape);
    
    /**
     * @brief Get drill shape from canonical name
     */
    QtPadDrillShape getDrillShapeFromCanonicalName(const QString& name);
    
    /**
     * @brief Validate padstack configuration
     */
    bool validatePadStackConfiguration(const EDA_PADSTACK_DATA* padStack, QString* error = nullptr);
    
    /**
     * @brief Calculate thermal spoke angle for given shape
     */
    double calculateDefaultThermalSpokeAngle(QtPadShape shape);
    
    /**
     * @brief Check if two padstacks are geometrically equivalent
     */
    bool areGeometricallyEquivalent(const EDA_PADSTACK_DATA* stack1, const EDA_PADSTACK_DATA* stack2);
}

