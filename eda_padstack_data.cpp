/*
 * Qt-based reimplementation of KiCad PADSTACK class - Implementation
 * 
 * This file contains the implementation of EDA_PADSTACK_DATA class providing
 * comprehensive padstack functionality using Qt frameworks.
 */

#include "eda_padstack_data.h"
#include "eda_board_object_data.h"
#include "eda_brd_shape_data.h"

#include <QtCore/QDebug>
#include <QtCore/QCoreApplication>
#include <QtCore/QLoggingCategory>
#include <QtCore/QMetaEnum>
#include <cmath>
#include <algorithm>

// Logging category removed (Qt object system removal)

//=============================================================================
// EDA_PADSTACK_DATA Implementation
//=============================================================================

EDA_PADSTACK_DATA::EDA_PADSTACK_DATA(EDA_BOARD_OBJECT_DATA* parent)
    : 
     m_parent(parent)
    , m_type(QtPadStackType::Normal)
    , m_mode(QtPadStackMode::Normal)
    , m_layerSet(QtLayerSet(QtPcbLayerId::FCu))
    , m_orientation(0.0)
    , m_unconnectedLayerMode(QtUnconnectedLayerMode::KeepAll)
    , m_customShapeZoneMode(QtCustomShapeZoneMode::Outline)
{
    initializeDefaultProperties();
}

EDA_PADSTACK_DATA::EDA_PADSTACK_DATA(const EDA_PADSTACK_DATA& other)
    : // QObject copy constructor removed
    m_parent(other.m_parent)
    , m_type(other.m_type)
    , m_mode(other.m_mode)
    , m_layerSet(other.m_layerSet)
    , m_customName(other.m_customName)
    , m_orientation(other.m_orientation)
    , m_copperProps(other.m_copperProps)
    , m_frontMaskProps(other.m_frontMaskProps)
    , m_backMaskProps(other.m_backMaskProps)
    , m_unconnectedLayerMode(other.m_unconnectedLayerMode)
    , m_customShapeZoneMode(other.m_customShapeZoneMode)
    , m_drill(other.m_drill)
    , m_secondaryDrill(other.m_secondaryDrill)
{
    deepCopyCustomShapes(other);
}

EDA_PADSTACK_DATA::~EDA_PADSTACK_DATA()
{
}

EDA_PADSTACK_DATA& EDA_PADSTACK_DATA::operator=(const EDA_PADSTACK_DATA& other)
{
    if (this != &other) {
        m_parent = other.m_parent;
        m_type = other.m_type;
        m_mode = other.m_mode;
        m_layerSet = other.m_layerSet;
        m_customName = other.m_customName;
        m_orientation = other.m_orientation;
        m_copperProps = other.m_copperProps;
        m_frontMaskProps = other.m_frontMaskProps;
        m_backMaskProps = other.m_backMaskProps;
        m_unconnectedLayerMode = other.m_unconnectedLayerMode;
        m_customShapeZoneMode = other.m_customShapeZoneMode;
        m_drill = other.m_drill;
        m_secondaryDrill = other.m_secondaryDrill;
        
        deepCopyCustomShapes(other);
    }
    return *this;
}

//=============================================================================
// Comparison and Similarity
//=============================================================================

bool EDA_PADSTACK_DATA::operator==(const EDA_PADSTACK_DATA& other) const
{
    return m_type == other.m_type &&
           m_mode == other.m_mode &&
           m_layerSet == other.m_layerSet &&
           m_customName == other.m_customName &&
           qFuzzyCompare(m_orientation, other.m_orientation) &&
           m_copperProps == other.m_copperProps &&
           m_frontMaskProps == other.m_frontMaskProps &&
           m_backMaskProps == other.m_backMaskProps &&
           m_unconnectedLayerMode == other.m_unconnectedLayerMode &&
           m_customShapeZoneMode == other.m_customShapeZoneMode &&
           m_drill == other.m_drill &&
           m_secondaryDrill == other.m_secondaryDrill;
}

double EDA_PADSTACK_DATA::similarity(const EDA_PADSTACK_DATA& other) const
{
    double score = 0.0;
    
    // Type and mode similarity (25%)
    if (m_type == other.m_type) score += 0.125;
    if (m_mode == other.m_mode) score += 0.125;
    
    // Layer set similarity (15%)
    QtLayerSet intersection = m_layerSet & other.m_layerSet;
    QtLayerSet union_set = m_layerSet | other.m_layerSet;
    if (!union_set.empty()) {
        score += 0.15 * (double(intersection.count()) / double(union_set.count()));
    }
    
    // Drill similarity (20%)
    if (m_drill == other.m_drill) {
        score += 0.2;
    } else if (m_drill.hasHole() && other.m_drill.hasHole()) {
        // Partial similarity based on drill size
        double sizeDiff = std::abs(m_drill.size.width() - other.m_drill.size.width()) +
                         std::abs(m_drill.size.height() - other.m_drill.size.height());
        score += 0.1 * std::max(0.0, 1.0 - sizeDiff / 10.0);  // Assuming 10mm is max difference
    }
    
    // Shape similarity for key layers (40%)
    QList<QtPcbLayerId> relevantLayers = uniqueLayers();
    if (!relevantLayers.isEmpty()) {
        double shapeScore = 0.0;
        for (QtPcbLayerId layer : relevantLayers) {
            const QtCopperLayerProperties& props1 = copperLayer(layer);
            const QtCopperLayerProperties& props2 = other.copperLayer(layer);
            
            if (props1.shape == props2.shape) {
                shapeScore += 1.0;
            }
        }
        score += 0.4 * (shapeScore / relevantLayers.size());
    }
    
    return qBound(0.0, score, 1.0);
}

int EDA_PADSTACK_DATA::compare(const EDA_PADSTACK_DATA* stackRef, const EDA_PADSTACK_DATA* stackCmp)
{
    if (!stackRef || !stackCmp) {
        return stackRef ? 1 : (stackCmp ? -1 : 0);
    }
    
    // Compare by type first
    if (stackRef->m_type != stackCmp->m_type) {
        return static_cast<int>(stackRef->m_type) - static_cast<int>(stackCmp->m_type);
    }
    
    // Then by mode
    if (stackRef->m_mode != stackCmp->m_mode) {
        return static_cast<int>(stackRef->m_mode) - static_cast<int>(stackCmp->m_mode);
    }
    
    // Then by drill size
    QSizeF size1 = stackRef->m_drill.size;
    QSizeF size2 = stackCmp->m_drill.size;
    
    if (!qFuzzyCompare(size1.width(), size2.width())) {
        return size1.width() < size2.width() ? -1 : 1;
    }
    
    if (!qFuzzyCompare(size1.height(), size2.height())) {
        return size1.height() < size2.height() ? -1 : 1;
    }
    
    return 0;
}

//=============================================================================
// Basic Properties
//=============================================================================

void EDA_PADSTACK_DATA::setMode(QtPadStackMode mode)
{
    if (m_mode != mode) {
        QtPadStackMode oldMode = m_mode;
        m_mode = mode;
        
        // Reorganize copper properties based on new mode
        if (oldMode == QtPadStackMode::Normal && mode != QtPadStackMode::Normal) {
            // Moving from normal to multi-layer mode
            QtCopperLayerProperties defaultProps = m_copperProps.value(QtPcbLayerId::FCu);
            
            if (mode == QtPadStackMode::FrontInnerBack) {
                m_copperProps[QtPcbLayerId::BCu] = defaultProps;
                m_copperProps[INNER_LAYERS] = defaultProps;
            }
        } else if (oldMode != QtPadStackMode::Normal && mode == QtPadStackMode::Normal) {
            // Moving to normal mode - keep only F_Cu properties
            QtCopperLayerProperties frontProps = m_copperProps.value(QtPcbLayerId::FCu);
            m_copperProps.clear();
            m_copperProps[QtPcbLayerId::FCu] = frontProps;
        }
        
        // Mode changed notification removed (Qt object system removal)
    }
}

void EDA_PADSTACK_DATA::setLayerSet(const QtLayerSet& layerSet)
{
    if (!(m_layerSet == layerSet)) {
        m_layerSet = layerSet;
        // Layer set changed notification removed (Qt object system removal)
    }
}

void EDA_PADSTACK_DATA::setCustomName(const QString& name)
{
    if (m_customName != name) {
        m_customName = name;
        // Custom name changed notification removed (Qt object system removal)
    }
}

QString EDA_PADSTACK_DATA::name() const
{
    if (!m_customName.isEmpty()) {
        return m_customName;
    }
    
    // Generate IPC-7351 format name
    QString shapeName = QtPadStackUtils::getShapeCanonicalName(shape());
    QSizeF padSize = size();
    
    QString result = QStringLiteral("%1_%2x%3")
                    .arg(shapeName)
                    .arg(padSize.width(), 0, 'f', 2)
                    .arg(padSize.height(), 0, 'f', 2);
    
    if (hasPrimaryDrill()) {
        QSizeF drillSize = m_drill.size;
        result += QStringLiteral("_D%1").arg(drillSize.width(), 0, 'f', 2);
        if (!m_drill.isCircular()) {
            result += QStringLiteral("x%1").arg(drillSize.height(), 0, 'f', 2);
        }
    }
    
    return result;
}

void EDA_PADSTACK_DATA::setOrientation(double degrees)
{
    // Normalize angle to 0-360 range
    double normalizedAngle = std::fmod(degrees, 360.0);
    if (normalizedAngle < 0.0) {
        normalizedAngle += 360.0;
    }
    
    if (!qFuzzyCompare(m_orientation, normalizedAngle)) {
        m_orientation = normalizedAngle;
        // Orientation changed notification removed (Qt object system removal)
    }
}

//=============================================================================
// Layer Management
//=============================================================================

QtPcbLayerId EDA_PADSTACK_DATA::startLayer() const
{
    return m_drill.startLayer;
}

QtPcbLayerId EDA_PADSTACK_DATA::endLayer() const
{
    return m_drill.endLayer;
}

void EDA_PADSTACK_DATA::flipLayers(int copperLayerCount)
{
    Q_UNUSED(copperLayerCount)
    
    // Flip the layer set
    QtLayerSet flippedLayers;
    for (int i = 0; i < static_cast<int>(QtPcbLayerId::LayerCount); ++i) {
        QtPcbLayerId layer = static_cast<QtPcbLayerId>(i);
        if (m_layerSet.test(layer)) {
            // Map to flipped layer
            QtPcbLayerId flippedLayer = layer;  // Simplified - would need proper layer mapping
            flippedLayers.set(flippedLayer);
        }
    }
    m_layerSet = flippedLayers;
    
    // Flip drill layers
    std::swap(m_drill.startLayer, m_drill.endLayer);
    std::swap(m_secondaryDrill.startLayer, m_secondaryDrill.endLayer);
    
    // Flip mask properties
    std::swap(m_frontMaskProps, m_backMaskProps);
    
    // Flip copper properties if in FrontInnerBack or Custom mode
    if (m_mode == QtPadStackMode::FrontInnerBack) {
        QtCopperLayerProperties frontProps = m_copperProps.value(QtPcbLayerId::FCu);
        QtCopperLayerProperties backProps = m_copperProps.value(QtPcbLayerId::BCu);
        
        m_copperProps[QtPcbLayerId::FCu] = backProps;
        m_copperProps[QtPcbLayerId::BCu] = frontProps;
    }
    // For Custom mode, would need more complex layer mapping
    
}

QtPcbLayerId EDA_PADSTACK_DATA::effectiveLayerFor(QtPcbLayerId layer) const
{
    switch (m_mode) {
    case QtPadStackMode::Normal:
        return ALL_LAYERS;  // All layers use F_Cu properties
        
    case QtPadStackMode::FrontInnerBack:
        if (layer == QtPcbLayerId::FCu) {
            return QtPcbLayerId::FCu;
        } else if (layer == QtPcbLayerId::BCu) {
            return QtPcbLayerId::BCu;
        } else {
            return INNER_LAYERS;  // All inner layers use In1_Cu properties
        }
        
    case QtPadStackMode::Custom:
        return layer;  // Each layer has its own properties
    }
    
    return ALL_LAYERS;
}

QList<QtPcbLayerId> EDA_PADSTACK_DATA::uniqueLayers() const
{
    QList<QtPcbLayerId> layers;
    
    switch (m_mode) {
    case QtPadStackMode::Normal:
        layers << ALL_LAYERS;
        break;
        
    case QtPadStackMode::FrontInnerBack:
        layers << QtPcbLayerId::FCu << INNER_LAYERS << QtPcbLayerId::BCu;
        break;
        
    case QtPadStackMode::Custom:
        // Return all layers that have properties defined
        for (auto it = m_copperProps.constBegin(); it != m_copperProps.constEnd(); ++it) {
            layers << it.key();
        }
        break;
    }
    
    return layers;
}

void EDA_PADSTACK_DATA::forEachUniqueLayer(const std::function<void(QtPcbLayerId)>& method) const
{
    QList<QtPcbLayerId> layers = uniqueLayers();
    for (QtPcbLayerId layer : layers) {
        method(layer);
    }
}

QtLayerSet EDA_PADSTACK_DATA::relevantShapeLayers(const EDA_PADSTACK_DATA& other) const
{
    QtLayerSet result;
    
    QList<QtPcbLayerId> myLayers = uniqueLayers();
    QList<QtPcbLayerId> otherLayers = other.uniqueLayers();
    
    // Combine unique layers from both padstacks
    for (QtPcbLayerId layer : myLayers) {
        result.set(layer);
    }
    for (QtPcbLayerId layer : otherLayers) {
        result.set(layer);
    }
    
    return result;
}

//=============================================================================
// Drill Properties
//=============================================================================

void EDA_PADSTACK_DATA::setDrill(const QtDrillProperties& drill)
{
    if (!(m_drill == drill)) {
        m_drill = drill;
    }
}

void EDA_PADSTACK_DATA::setSecondaryDrill(const QtDrillProperties& drill)
{
    if (!(m_secondaryDrill == drill)) {
        m_secondaryDrill = drill;
    }
}

void EDA_PADSTACK_DATA::setDrillShape(QtPadDrillShape shape)
{
    if (m_drill.shape != shape) {
        m_drill.shape = shape;
    }
}

//=============================================================================
// Unconnected Layer Handling
//=============================================================================

void EDA_PADSTACK_DATA::setUnconnectedLayerMode(QtUnconnectedLayerMode mode)
{
    if (m_unconnectedLayerMode != mode) {
        m_unconnectedLayerMode = mode;
    }
}

//=============================================================================
// Custom Shape Zone Mode
//=============================================================================

void EDA_PADSTACK_DATA::setCustomShapeZoneMode(QtCustomShapeZoneMode mode)
{
    if (m_customShapeZoneMode != mode) {
        m_customShapeZoneMode = mode;
    }
}

//=============================================================================
// Copper Layer Access
//=============================================================================

QtCopperLayerProperties& EDA_PADSTACK_DATA::copperLayer(QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    return m_copperProps[effectiveLayer];
}

const QtCopperLayerProperties& EDA_PADSTACK_DATA::copperLayer(QtPcbLayerId layer) const
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    auto it = m_copperProps.find(effectiveLayer);
    if (it != m_copperProps.end()) {
        return it.value();
    }
    
    // Return default properties if not found
    static QtCopperLayerProperties defaultProps;
    return defaultProps;
}

//=============================================================================
// Mask Layer Access
//=============================================================================

std::optional<bool> EDA_PADSTACK_DATA::isTented(QtPcbLayerId side) const
{
    if (side == QtPcbLayerId::FCu || side == QtPcbLayerId::FMask) {
        return m_frontMaskProps.hasSolderMask;
    } else if (side == QtPcbLayerId::BCu || side == QtPcbLayerId::BMask) {
        return m_backMaskProps.hasSolderMask;
    }
    
    return std::nullopt;  // No override, use design rules
}

//=============================================================================
// Shape Properties Convenience Methods
//=============================================================================

QtPadShape EDA_PADSTACK_DATA::shape(QtPcbLayerId layer) const
{
    return copperLayer(layer).shape.shape;
}

void EDA_PADSTACK_DATA::setShape(QtPadShape shape, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].shape.shape != shape) {
        m_copperProps[effectiveLayer].shape.shape = shape;
    }
}

QSizeF EDA_PADSTACK_DATA::size(QtPcbLayerId layer) const
{
    return copperLayer(layer).shape.size;
}

void EDA_PADSTACK_DATA::setSize(const QSizeF& size, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].shape.size != size) {
        m_copperProps[effectiveLayer].shape.size = size;
    }
}

QPointF EDA_PADSTACK_DATA::offset(QtPcbLayerId layer) const
{
    return copperLayer(layer).shape.offset;
}

void EDA_PADSTACK_DATA::setOffset(const QPointF& offset, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].shape.offset != offset) {
        m_copperProps[effectiveLayer].shape.offset = offset;
    }
}

QtPadShape EDA_PADSTACK_DATA::anchorShape(QtPcbLayerId layer) const
{
    return copperLayer(layer).shape.anchorShape;
}

void EDA_PADSTACK_DATA::setAnchorShape(QtPadShape shape, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].shape.anchorShape != shape) {
        m_copperProps[effectiveLayer].shape.anchorShape = shape;
    }
}

QSizeF EDA_PADSTACK_DATA::trapezoidDeltaSize(QtPcbLayerId layer) const
{
    return copperLayer(layer).shape.trapezoidDelta;
}

void EDA_PADSTACK_DATA::setTrapezoidDeltaSize(const QSizeF& delta, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].shape.trapezoidDelta != delta) {
        m_copperProps[effectiveLayer].shape.trapezoidDelta = delta;
    }
}

double EDA_PADSTACK_DATA::roundRectRadiusRatio(QtPcbLayerId layer) const
{
    return copperLayer(layer).shape.roundRectRadiusRatio;
}

void EDA_PADSTACK_DATA::setRoundRectRadiusRatio(double ratio, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    double clampedRatio = qBound(0.0, ratio, 0.5);
    if (!qFuzzyCompare(m_copperProps[effectiveLayer].shape.roundRectRadiusRatio, clampedRatio)) {
        m_copperProps[effectiveLayer].shape.roundRectRadiusRatio = clampedRatio;
    }
}

double EDA_PADSTACK_DATA::roundRectRadius(QtPcbLayerId layer) const
{
    return copperLayer(layer).shape.roundRectCornerRadius;
}

void EDA_PADSTACK_DATA::setRoundRectRadius(double radius, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (!qFuzzyCompare(m_copperProps[effectiveLayer].shape.roundRectCornerRadius, radius)) {
        m_copperProps[effectiveLayer].shape.roundRectCornerRadius = radius;
    }
}

double EDA_PADSTACK_DATA::chamferRatio(QtPcbLayerId layer) const
{
    return copperLayer(layer).shape.chamferRatio;
}

void EDA_PADSTACK_DATA::setChamferRatio(double ratio, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    double clampedRatio = qBound(0.0, ratio, 0.5);
    if (!qFuzzyCompare(m_copperProps[effectiveLayer].shape.chamferRatio, clampedRatio)) {
        m_copperProps[effectiveLayer].shape.chamferRatio = clampedRatio;
    }
}

int EDA_PADSTACK_DATA::chamferPositions(QtPcbLayerId layer) const
{
    return copperLayer(layer).shape.chamferPositions;
}

void EDA_PADSTACK_DATA::setChamferPositions(int positions, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].shape.chamferPositions != positions) {
        m_copperProps[effectiveLayer].shape.chamferPositions = positions;
    }
}

//=============================================================================
// Electrical Properties Convenience Methods
//=============================================================================

std::optional<int> EDA_PADSTACK_DATA::clearance(QtPcbLayerId layer) const
{
    return copperLayer(layer).clearance;
}

void EDA_PADSTACK_DATA::setClearance(std::optional<int> clearance, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].clearance != clearance) {
        m_copperProps[effectiveLayer].clearance = clearance;
    }
}

std::optional<int> EDA_PADSTACK_DATA::solderMaskMargin(QtPcbLayerId layer) const
{
    if (layer == QtPcbLayerId::FCu || layer == ALL_LAYERS) {
        return m_frontMaskProps.solderMaskMargin;
    } else if (layer == QtPcbLayerId::BCu) {
        return m_backMaskProps.solderMaskMargin;
    }
    return std::nullopt;
}

void EDA_PADSTACK_DATA::setSolderMaskMargin(std::optional<int> margin, QtPcbLayerId layer)
{
    if (layer == QtPcbLayerId::FCu || layer == ALL_LAYERS) {
        if (m_frontMaskProps.solderMaskMargin != margin) {
            m_frontMaskProps.solderMaskMargin = margin;
        }
    } else if (layer == QtPcbLayerId::BCu) {
        if (m_backMaskProps.solderMaskMargin != margin) {
            m_backMaskProps.solderMaskMargin = margin;
        }
    }
}

std::optional<int> EDA_PADSTACK_DATA::solderPasteMargin(QtPcbLayerId layer) const
{
    if (layer == QtPcbLayerId::FCu || layer == ALL_LAYERS) {
        return m_frontMaskProps.solderPasteMargin;
    } else if (layer == QtPcbLayerId::BCu) {
        return m_backMaskProps.solderPasteMargin;
    }
    return std::nullopt;
}

void EDA_PADSTACK_DATA::setSolderPasteMargin(std::optional<int> margin, QtPcbLayerId layer)
{
    if (layer == QtPcbLayerId::FCu || layer == ALL_LAYERS) {
        if (m_frontMaskProps.solderPasteMargin != margin) {
            m_frontMaskProps.solderPasteMargin = margin;
        }
    } else if (layer == QtPcbLayerId::BCu) {
        if (m_backMaskProps.solderPasteMargin != margin) {
            m_backMaskProps.solderPasteMargin = margin;
        }
    }
}

std::optional<double> EDA_PADSTACK_DATA::solderPasteMarginRatio(QtPcbLayerId layer) const
{
    if (layer == QtPcbLayerId::FCu || layer == ALL_LAYERS) {
        return m_frontMaskProps.solderPasteMarginRatio;
    } else if (layer == QtPcbLayerId::BCu) {
        return m_backMaskProps.solderPasteMarginRatio;
    }
    return std::nullopt;
}

void EDA_PADSTACK_DATA::setSolderPasteMarginRatio(std::optional<double> ratio, QtPcbLayerId layer)
{
    if (layer == QtPcbLayerId::FCu || layer == ALL_LAYERS) {
        if (m_frontMaskProps.solderPasteMarginRatio != ratio) {
            m_frontMaskProps.solderPasteMarginRatio = ratio;
        }
    } else if (layer == QtPcbLayerId::BCu) {
        if (m_backMaskProps.solderPasteMarginRatio != ratio) {
            m_backMaskProps.solderPasteMarginRatio = ratio;
        }
    }
}

std::optional<QtZoneConnection> EDA_PADSTACK_DATA::zoneConnection(QtPcbLayerId layer) const
{
    return copperLayer(layer).zoneConnection;
}

void EDA_PADSTACK_DATA::setZoneConnection(std::optional<QtZoneConnection> connection, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].zoneConnection != connection) {
        m_copperProps[effectiveLayer].zoneConnection = connection;
    }
}

std::optional<int> EDA_PADSTACK_DATA::thermalSpokeWidth(QtPcbLayerId layer) const
{
    return copperLayer(layer).thermalSpokeWidth;
}

void EDA_PADSTACK_DATA::setThermalSpokeWidth(std::optional<int> width, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].thermalSpokeWidth != width) {
        m_copperProps[effectiveLayer].thermalSpokeWidth = width;
    }
}

std::optional<int> EDA_PADSTACK_DATA::thermalGap(QtPcbLayerId layer) const
{
    return copperLayer(layer).thermalGap;
}

void EDA_PADSTACK_DATA::setThermalGap(std::optional<int> gap, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].thermalGap != gap) {
        m_copperProps[effectiveLayer].thermalGap = gap;
    }
}

double EDA_PADSTACK_DATA::thermalSpokeAngle(QtPcbLayerId layer) const
{
    std::optional<double> angle = copperLayer(layer).thermalSpokeAngle;
    if (angle.has_value()) {
        return angle.value();
    }
    return defaultThermalSpokeAngleForShape(layer);
}

void EDA_PADSTACK_DATA::setThermalSpokeAngle(double degrees, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    if (m_copperProps[effectiveLayer].thermalSpokeAngle != degrees) {
        m_copperProps[effectiveLayer].thermalSpokeAngle = degrees;
    }
}

double EDA_PADSTACK_DATA::defaultThermalSpokeAngleForShape(QtPcbLayerId layer) const
{
    return QtPadStackUtils::calculateDefaultThermalSpokeAngle(shape(layer));
}

//=============================================================================
// Custom Shape Primitives
//=============================================================================

QList<QSharedPointer<EDA_BRD_SHAPE_DATA>>& EDA_PADSTACK_DATA::primitives(QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    return m_copperProps[effectiveLayer].customShapes;
}

const QList<QSharedPointer<EDA_BRD_SHAPE_DATA>>& EDA_PADSTACK_DATA::primitives(QtPcbLayerId layer) const
{
    const QtCopperLayerProperties& props = copperLayer(layer);
    return props.customShapes;
}

void EDA_PADSTACK_DATA::addPrimitive(QSharedPointer<EDA_BRD_SHAPE_DATA> shape, QtPcbLayerId layer)
{
    if (shape) {
        QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
        ensureCopperLayer(effectiveLayer);
        m_copperProps[effectiveLayer].customShapes.append(shape);
    }
}

void EDA_PADSTACK_DATA::appendPrimitives(const QList<QSharedPointer<EDA_BRD_SHAPE_DATA>>& primitives, QtPcbLayerId layer)
{
    if (!primitives.isEmpty()) {
        QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
        ensureCopperLayer(effectiveLayer);
        
        for (const auto& primitive : primitives) {
            if (primitive) {
                // Deep copy the primitive
                // m_copperProps[effectiveLayer].customShapes.append(QSharedPointer<EDA_BRD_SHAPE_DATA>(primitive->clone()));
                m_copperProps[effectiveLayer].customShapes.append(primitive);  // Simplified for now
            }
        }
    }
}

void EDA_PADSTACK_DATA::replacePrimitives(const QList<QSharedPointer<EDA_BRD_SHAPE_DATA>>& primitives, QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    ensureCopperLayer(effectiveLayer);
    
    m_copperProps[effectiveLayer].customShapes.clear();
    appendPrimitives(primitives, layer);
}

void EDA_PADSTACK_DATA::clearPrimitives(QtPcbLayerId layer)
{
    QtPcbLayerId effectiveLayer = effectiveLayerFor(layer);
    if (m_copperProps.contains(effectiveLayer)) {
        if (!m_copperProps[effectiveLayer].customShapes.isEmpty()) {
            m_copperProps[effectiveLayer].customShapes.clear();
        }
    }
}

//=============================================================================
// Qt Serialization
//=============================================================================

QVariantMap EDA_PADSTACK_DATA::toVariantMap() const
{
    QVariantMap map;
    
    map["type"] = static_cast<int>(m_type);
    map["mode"] = static_cast<int>(m_mode);
    map["customName"] = m_customName;
    map["orientation"] = m_orientation;
    map["unconnectedLayerMode"] = static_cast<int>(m_unconnectedLayerMode);
    map["customShapeZoneMode"] = static_cast<int>(m_customShapeZoneMode);
    
    // Serialize layer set
    QVariantList layerList;
    for (int i = 0; i < static_cast<int>(QtPcbLayerId::LayerCount); ++i) {
        QtPcbLayerId layer = static_cast<QtPcbLayerId>(i);
        if (m_layerSet.test(layer)) {
            layerList.append(i);
        }
    }
    map["layerSet"] = layerList;
    
    // Serialize drill properties
    QVariantMap drillMap;
    drillMap["width"] = m_drill.size.width();
    drillMap["height"] = m_drill.size.height();
    drillMap["shape"] = static_cast<int>(m_drill.shape);
    drillMap["startLayer"] = static_cast<int>(m_drill.startLayer);
    drillMap["endLayer"] = static_cast<int>(m_drill.endLayer);
    map["drill"] = drillMap;
    
    QVariantMap secondaryDrillMap;
    secondaryDrillMap["width"] = m_secondaryDrill.size.width();
    secondaryDrillMap["height"] = m_secondaryDrill.size.height();
    secondaryDrillMap["shape"] = static_cast<int>(m_secondaryDrill.shape);
    secondaryDrillMap["startLayer"] = static_cast<int>(m_secondaryDrill.startLayer);
    secondaryDrillMap["endLayer"] = static_cast<int>(m_secondaryDrill.endLayer);
    map["secondaryDrill"] = secondaryDrillMap;
    
    // TODO: Serialize copper properties, mask properties, and custom shapes
    
    return map;
}

void EDA_PADSTACK_DATA::fromVariantMap(const QVariantMap& map)
{
    m_type = static_cast<QtPadStackType>(map.value("type", 0).toInt());
    m_mode = static_cast<QtPadStackMode>(map.value("mode", 0).toInt());
    m_customName = map.value("customName").toString();
    m_orientation = map.value("orientation", 0.0).toDouble();
    m_unconnectedLayerMode = static_cast<QtUnconnectedLayerMode>(map.value("unconnectedLayerMode", 0).toInt());
    m_customShapeZoneMode = static_cast<QtCustomShapeZoneMode>(map.value("customShapeZoneMode", 0).toInt());
    
    // Deserialize layer set
    QVariantList layerList = map.value("layerSet").toList();
    m_layerSet.reset();
    for (const QVariant& layerVar : layerList) {
        int layerId = layerVar.toInt();
        if (layerId >= 0 && layerId < static_cast<int>(QtPcbLayerId::LayerCount)) {
            m_layerSet.set(static_cast<QtPcbLayerId>(layerId));
        }
    }
    
    // Deserialize drill properties
    QVariantMap drillMap = map.value("drill").toMap();
    m_drill.size.setWidth(drillMap.value("width", 0.0).toDouble());
    m_drill.size.setHeight(drillMap.value("height", 0.0).toDouble());
    m_drill.shape = static_cast<QtPadDrillShape>(drillMap.value("shape", 0).toInt());
    m_drill.startLayer = static_cast<QtPcbLayerId>(drillMap.value("startLayer", 0).toInt());
    m_drill.endLayer = static_cast<QtPcbLayerId>(drillMap.value("endLayer", 0).toInt());
    
    QVariantMap secondaryDrillMap = map.value("secondaryDrill").toMap();
    m_secondaryDrill.size.setWidth(secondaryDrillMap.value("width", 0.0).toDouble());
    m_secondaryDrill.size.setHeight(secondaryDrillMap.value("height", 0.0).toDouble());
    m_secondaryDrill.shape = static_cast<QtPadDrillShape>(secondaryDrillMap.value("shape", 0).toInt());
    m_secondaryDrill.startLayer = static_cast<QtPcbLayerId>(secondaryDrillMap.value("startLayer", 0).toInt());
    m_secondaryDrill.endLayer = static_cast<QtPcbLayerId>(secondaryDrillMap.value("endLayer", 0).toInt());
    
    // TODO: Deserialize copper properties, mask properties, and custom shapes
}

QString EDA_PADSTACK_DATA::toString() const
{
    return QStringLiteral("EDA_PADSTACK_DATA(type=%1, mode=%2, shape=%3, size=%4x%5)")
           .arg(static_cast<int>(m_type))
           .arg(static_cast<int>(m_mode))
           .arg(static_cast<int>(shape()))
           .arg(size().width())
           .arg(size().height());
}

//=============================================================================
// Private Helper Methods
//=============================================================================

void EDA_PADSTACK_DATA::ensureCopperLayer(QtPcbLayerId layer)
{
    if (!m_copperProps.contains(layer)) {
        m_copperProps[layer] = QtCopperLayerProperties();
    }
}

void EDA_PADSTACK_DATA::deepCopyCustomShapes(const EDA_PADSTACK_DATA& other)
{
    // Deep copy all custom shapes from other padstack
    for (auto it = other.m_copperProps.constBegin(); it != other.m_copperProps.constEnd(); ++it) {
        QtPcbLayerId layer = it.key();
        const QtCopperLayerProperties& otherProps = it.value();
        
        if (m_copperProps.contains(layer)) {
            m_copperProps[layer].customShapes.clear();
            
            for (const auto& shape : otherProps.customShapes) {
                if (shape) {
                    // Deep copy the shape
                    // m_copperProps[layer].customShapes.append(QSharedPointer<EDA_BRD_SHAPE_DATA>(shape->clone()));
                    m_copperProps[layer].customShapes.append(shape);  // Simplified for now
                }
            }
        }
    }
}

void EDA_PADSTACK_DATA::initializeDefaultProperties()
{
    // Initialize default copper layer properties
    QtCopperLayerProperties defaultProps;
    defaultProps.shape.shape = QtPadShape::Circle;
    defaultProps.shape.size = QSizeF(1.0, 1.0);
    
    m_copperProps[ALL_LAYERS] = defaultProps;
}

//=============================================================================
// QtPadStackUtils Namespace Implementation
//=============================================================================

namespace QtPadStackUtils {

QString getShapeCanonicalName(QtPadShape shape)
{
    switch (shape) {
        case QtPadShape::Circle: return "circle";
        case QtPadShape::Rectangle: return "rect";
        case QtPadShape::Oval: return "oval";
        case QtPadShape::Trapezoid: return "trapezoid";
        case QtPadShape::RoundRect: return "roundrect";
        case QtPadShape::ChamferedRect: return "chamfered";
        case QtPadShape::Custom: return "custom";
        default: return "circle";
    }
}

QtPadShape getShapeFromCanonicalName(const QString& name)
{
    if (name == "circle") return QtPadShape::Circle;
    if (name == "rect") return QtPadShape::Rectangle;
    if (name == "oval") return QtPadShape::Oval;
    if (name == "trapezoid") return QtPadShape::Trapezoid;
    if (name == "roundrect") return QtPadShape::RoundRect;
    if (name == "chamfered") return QtPadShape::ChamferedRect;
    if (name == "custom") return QtPadShape::Custom;
    return QtPadShape::Circle;
}

QString getDrillShapeCanonicalName(QtPadDrillShape shape)
{
    switch (shape) {
        case QtPadDrillShape::Circle: return "circle";
        case QtPadDrillShape::Oval: return "oval";
        default: return "circle";
    }
}

QtPadDrillShape getDrillShapeFromCanonicalName(const QString& name)
{
    if (name == "circle") return QtPadDrillShape::Circle;
    if (name == "oval") return QtPadDrillShape::Oval;
    return QtPadDrillShape::Circle;
}

bool validatePadStackConfiguration(const EDA_PADSTACK_DATA* padStack, QString* error)
{
    if (!padStack) {
        if (error) *error = QCoreApplication::translate("QtPadStackUtils", "Null padstack pointer");
        return false;
    }
    
    // Validate size
    QSizeF size = padStack->size();
    if (size.width() <= 0.0 || size.height() <= 0.0) {
        if (error) *error = QCoreApplication::translate("QtPadStackUtils", "Invalid padstack size");
        return false;
    }
    
    // Validate drill consistency
    if (padStack->hasPrimaryDrill()) {
        QtDrillProperties drill = padStack->drill();
        if (drill.size.width() <= 0.0 || drill.size.height() <= 0.0) {
            if (error) *error = QCoreApplication::translate("QtPadStackUtils", "Invalid drill size");
            return false;
        }
        
        // Check drill vs pad size
        if (drill.size.width() >= size.width() || drill.size.height() >= size.height()) {
            if (error) *error = QCoreApplication::translate("QtPadStackUtils", "Drill larger than pad");
            return false;
        }
    }
    
    return true;
}

double calculateDefaultThermalSpokeAngle(QtPadShape shape)
{
    switch (shape) {
    case QtPadShape::Circle:
    case QtPadShape::Oval:
        return 45.0;  // X pattern for circular/oval shapes
        
    case QtPadShape::Rectangle:
    case QtPadShape::RoundRect:
    case QtPadShape::ChamferedRect:
    case QtPadShape::Trapezoid:
        return 90.0;  // + pattern for rectangular shapes
        
    case QtPadShape::Custom:
        return 45.0;  // Default to X pattern for custom shapes
    }
    
    return 45.0;
}

bool areGeometricallyEquivalent(const EDA_PADSTACK_DATA* stack1, const EDA_PADSTACK_DATA* stack2)
{
    if (!stack1 || !stack2) {
        return false;
    }
    
    // Check if both stacks have the same unique layers
    QList<QtPcbLayerId> layers1 = stack1->uniqueLayers();
    QList<QtPcbLayerId> layers2 = stack2->uniqueLayers();
    
    if (layers1.size() != layers2.size()) {
        return false;
    }
    
    // Check shape properties on each layer
    for (QtPcbLayerId layer : layers1) {
        if (!layers2.contains(layer)) {
            return false;
        }
        
        // Compare shape properties
        QtPadStackShapeProperties props1 = stack1->copperLayer(layer).shape;
        QtPadStackShapeProperties props2 = stack2->copperLayer(layer).shape;
        
        if (props1 != props2) {
            return false;
        }
    }
    
    // Check drill properties
    if (stack1->drill() != stack2->drill()) {
        return false;
    }
    
    return true;
}

} // namespace QtPadStackUtils

