/*
 * This program source code file is part of KICAD, a free EDA CAD application.
 *
 * Copyright The KiCad Developers, see AUTHORS.txt for contributors.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, you may find one here:
 * http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
 * or you may search the http://www.gnu.org website for the version 2 license,
 * or you may write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA
 */

#ifndef QT_CONNECTIVITY_DATA_H
#define QT_CONNECTIVITY_DATA_H

#include <QObject>
#include <QString>
#include <QVector>
#include <QMap>
#include <QSet>
#include <QSharedPointer>
#include <QWeakPointer>
#include <QMutex>
#include <QMutexLocker>
#include <QVector2D>
#include <QPolygonF>
#include <functional>
#include <memory>
#include <initializer_list>

// 前向声明
class QtFromToCache;
class QtCnCluster;
class QtCnConnectivityAlgo;
class QtCnEdge;
class EDA_BOARD_DATA;
class QtBoardCommit;
class EDA_BOARD_CONNECTED_OBJECT;
class EDA_BOARD_OBJECT_DATA;
class EDA_ZONE_DATA;
class QtRnData;
class QtRnNet;
class EDA_TRACK_DATA;
class EDA_VIA_DATA;
class EDA_PAD_DATA;
class EDA_FOOTPRINT_DATA;
class QtProgressReporter;
class QtNetSettings;
class QtShapePolySet;
class QtIsolatedIslands;

// Qt版本的网络间断开连接项
struct QtCnDisjointNetEntry
{
    int net;
    EDA_BOARD_CONNECTED_OBJECT* a;
    EDA_BOARD_CONNECTED_OBJECT* b;
    QVector2D anchorA;
    QVector2D anchorB;
};

// Qt版本的动态线条
struct QtRnDynamicLine
{
    int netCode;
    QVector2D a;
    QVector2D b;
};

/**
 * 控制网络如何通过集群传播
 */
enum class QtPropagateMode
{
    SKIP_CONFLICTS,     // 有冲突驱动的集群不更新（默认）
    RESOLVE_CONFLICTS   // 有冲突驱动的集群更新为最流行的网络
};

/**
 * Qt版本的连接性数据管理类
 * 包含连接性计算算法和数据管理
 */
class EDA_CONNECTIVITY_MANAGER : public QObject
{

public:
    explicit EDA_CONNECTIVITY_MANAGER(QObject* parent = nullptr);
    explicit EDA_CONNECTIVITY_MANAGER(QSharedPointer<EDA_CONNECTIVITY_MANAGER> globalConnectivity,
                              const QVector<EDA_BOARD_OBJECT_DATA*>& localItems,
                              bool skipRatsnestUpdate = false,
                              QObject* parent = nullptr);
    ~EDA_CONNECTIVITY_MANAGER() override;

    // 构建功能
    bool build(EDA_BOARD_DATA* board, QtProgressReporter* reporter = nullptr);
    void build(QSharedPointer<EDA_CONNECTIVITY_MANAGER>& globalConnectivity,
               const QVector<EDA_BOARD_OBJECT_DATA*>& localItems);

    // 项目管理
    bool add(EDA_BOARD_OBJECT_DATA* item);
    bool remove(EDA_BOARD_OBJECT_DATA* item);
    bool update(EDA_BOARD_OBJECT_DATA* item);

    // 移动功能
    void move(const QVector2D& delta);

    // 清理功能
    void clearRatsnest();

    // 网络信息
    int getNetCount() const;
    QtRnNet* getRatsnestForNet(int net);

    // 网络传播
    void propagateNets(QtBoardCommit* commit = nullptr);

    // 隔离岛填充
    void fillIsolatedIslandsMap(QMap<EDA_ZONE_DATA*, QMap<int, QtIsolatedIslands>>& map,
                               bool connectivityAlreadyRebuilt = false);

    // 鼠线计算
    void recalculateRatsnest(QtBoardCommit* commit = nullptr);

    // 连接查询
    unsigned int getUnconnectedCount(bool visibleOnly) const;
    bool isConnectedOnLayer(const EDA_BOARD_CONNECTED_OBJECT* item, int layer,
                          const std::initializer_list<int>& types = {}) const;

    // 节点和焊盘统计
    unsigned int getNodeCount(int net = -1) const;
    unsigned int getPadCount(int net = -1) const;

    // 连接项查询
    QVector<EDA_TRACK_DATA*> getConnectedTracks(const EDA_BOARD_CONNECTED_OBJECT* item) const;
    QVector<EDA_PAD_DATA*> getConnectedPads(const EDA_BOARD_CONNECTED_OBJECT* item) const;
    void getConnectedPads(const EDA_BOARD_CONNECTED_OBJECT* item, QSet<EDA_PAD_DATA*>* pads) const;
    void getConnectedPadsAndVias(const EDA_BOARD_CONNECTED_OBJECT* item,
                                QVector<EDA_PAD_DATA*>* pads,
                                QVector<EDA_VIA_DATA*>* vias);

    // 锚点连接查询
    QVector<EDA_BOARD_CONNECTED_OBJECT*> getConnectedItemsAtAnchor(
        const EDA_BOARD_CONNECTED_OBJECT* item,
        const QVector2D& anchor,
        const QVector<int>& types,
        int maxError = 0) const;

    // 边缘处理
    void runOnUnconnectedEdges(std::function<bool(QtCnEdge&)> func);

    // 轨迹端点检测
    bool testTrackEndpointDangling(EDA_TRACK_DATA* track, bool ignoreTracksInPads,
                                  QVector2D* pos = nullptr) const;

    // 局部鼠线管理
    void clearLocalRatsnest();
    void hideLocalRatsnest();
    void computeLocalRatsnest(const QVector<EDA_BOARD_OBJECT_DATA*>& items,
                            const EDA_CONNECTIVITY_MANAGER* dynamicData,
                            QVector2D internalOffset = QVector2D(0, 0));
    const QVector<QtRnDynamicLine>& getLocalRatsnest() const { return m_dynamicRatsnest; }

    // 连接项查询
    QVector<EDA_BOARD_CONNECTED_OBJECT*> getConnectedItems(
        const EDA_BOARD_CONNECTED_OBJECT* item,
        const QVector<int>& types,
        bool ignoreNetcodes = false) const;

    // 网络项查询
    QVector<EDA_BOARD_CONNECTED_OBJECT*> getNetItems(int netCode, const QVector<int>& types) const;

    // 鼠线阻塞
    void blockRatsnestItems(const QVector<EDA_BOARD_OBJECT_DATA*>& items);

    // 算法访问
    QSharedPointer<QtCnConnectivityAlgo> getConnectivityAlgo() const { return m_connAlgo; }

    // 锁访问
    QMutex& getLock() { return m_lock; }

    // 标记功能
    void markItemNetAsDirty(EDA_BOARD_OBJECT_DATA* item);
    void removeInvalidRefs();

    // 进度报告
    void setProgressReporter(QtProgressReporter* reporter);

    // 网络设置
    const QtNetSettings* getNetSettings() const;

    // 网络代码映射
    bool hasNetNameForNetCode(int nc) const;
    const QString& getNetNameForNetCode(int nc) const;
    void refreshNetcodeMap(EDA_BOARD_DATA* board);

    // 鼠线获取（特定项）
    QVector<QtCnEdge> getRatsnestForItems(const QVector<EDA_BOARD_OBJECT_DATA*>& items);
    QVector<QtCnEdge> getRatsnestForPad(const EDA_PAD_DATA* pad);
    QVector<QtCnEdge> getRatsnestForComponent(EDA_FOOTPRINT_DATA* component,
                                             bool skipInternalConnections = false);

    // 从/到缓存
    QSharedPointer<QtFromToCache> getFromToCache() { return m_fromToCache; }

    // 属性访问
    bool isSkipRatsnestUpdate() const { return m_skipRatsnestUpdate; }
    void setSkipRatsnestUpdate(bool skip);




private:
    // 内部方法
    void internalRecalculateRatsnest(QtBoardCommit* commit = nullptr);
    void updateRatsnest();
    void addRatsnestCluster(const QSharedPointer<QtCnCluster>& cluster);

    // 验证方法
    bool validateItem(EDA_BOARD_OBJECT_DATA* item) const;
    void ensureInitialized();

private:
    // 核心数据
    QSharedPointer<QtCnConnectivityAlgo> m_connAlgo;       // 连接算法
    QSharedPointer<QtFromToCache> m_fromToCache;           // 从/到缓存
    QVector<QtRnDynamicLine> m_dynamicRatsnest;           // 动态鼠线
    QVector<QtRnNet*> m_nets;                             // 网络列表

    // 状态管理
    bool m_skipRatsnestUpdate;                             // 跳过鼠线更新标志
    mutable QMutex m_lock;                                 // 线程锁

    // 进度报告
    QtProgressReporter* m_progressReporter;                // 进度报告器

    // 网络设置
    QWeakPointer<QtNetSettings> m_netSettings;            // 网络设置

    // 网络代码映射
    QMap<int, QString> m_netcodeMap;                      // 网络代码到名称映射

    // 缓存数据
    mutable QMap<int, unsigned int> m_nodeCountCache;     // 节点数缓存
    mutable QMap<int, unsigned int> m_padCountCache;      // 焊盘数缓存
    mutable bool m_cacheValid;                            // 缓存有效标志
};

#endif /* QT_CONNECTIVITY_DATA_H */