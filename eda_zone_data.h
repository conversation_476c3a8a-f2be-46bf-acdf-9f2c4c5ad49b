#ifndef QT_ZONE_H
#define QT_ZONE_H

#include <QRectF>
#include <QPolygonF>
#include <QVector>
#include <QMap>
#include <QSet>
#include <QMutex>
#include <QSharedPointer>
#include <optional>

#include "eda_board_connected_object.h"
#include "qt_temporary_implementations.h"

// Forward declarations
class EDA_BOARD_OBJECT_CONTAINER;
class EDA_PAD_DATA;
class EDA_BOARD_DATA;
class QtEdaDrawFrame;
class QtUnitsProvider;
class QtView;
class QtShapePolySet;
class QtShapeLineChain;
class QtShape;
class QtSeg;
class QtSearchData;

// Import zone connection from padstack header
#include "eda_padstack_data.h"  // For QtZoneConnection enum

// Temporary type definitions
using QtHash128 = QByteArray;  // 128-bit hash


// Temporary teardrop type enum
enum class QtTeardropType {
    TD_NONE = 0,
    TD_VIAPAD,
    TD_TRACKEND
};

// QtLset is already defined in eda_board_object_data.h as QtLayerSet
// No need for type alias - using QtLayerSet directly

// Zone fill mode
enum class QtZoneFillMode
{
    POLYGONS = 0,      // Fill zone with polygons
    HATCH_PATTERN = 1  // Fill zone using a grid pattern
};

// Zone border display style
enum class QtZoneBorderDisplayStyle
{
    NO_HATCH,
    DIAGONAL_FULL,
    DIAGONAL_EDGE,
    INVISIBLE_BORDER   // Disable outline drawing for very special cases
};

// Island removal mode
enum class QtIslandRemovalMode
{
    ALWAYS = 0,
    NEVER,
    AREA
};

// Rule area placement source type
enum class QtRuleAreaPlacementSourceType
{
    SHEETNAME = 0,
    COMPONENT_CLASS
};

// Zone connection type - defined in eda_padstack_data.h
// Forward declaration only

// Isolated islands structure - defined in connectivity manager
// Forward declaration
class QtIsolatedIslands;

// Zone settings structure
struct QtZoneSettings
{
    // Smoothing types
    enum SmoothingType {
        SMOOTHING_UNDEFINED = -1,
        SMOOTHING_NONE = 0,
        SMOOTHING_CHAMFER,
        SMOOTHING_FILLET,
        SMOOTHING_LAST
    };

    unsigned zonePriority = 0;
    QtZoneFillMode fillMode = QtZoneFillMode::POLYGONS;
    int zoneClearance = 0;
    int zoneMinThickness = 0;
    int hatchThickness = 0;
    int hatchGap = 0;
    QtEdaAngle hatchOrientation;
    int hatchSmoothingLevel = 0;
    double hatchSmoothingValue = 0.0;
    double hatchHoleMinArea = 0.0;
    int hatchBorderAlgorithm = 0;
};

class EDA_ZONE_DATA : public EDA_BOARD_CONNECTED_OBJECT
{

public:
    explicit EDA_ZONE_DATA(EDA_BOARD_OBJECT_CONTAINER* parent = nullptr);
    EDA_ZONE_DATA(const EDA_ZONE_DATA& other);
    ~EDA_ZONE_DATA() override;
    
    EDA_ZONE_DATA& operator=(const EDA_ZONE_DATA& other);
    
    static bool classOf(const EDA_OBJECT_DATA* item);
    
    // String conversion helpers
    static QString fillModeToString(QtZoneFillMode mode);
    static QString connectionToString(QtZoneConnection connection);
    
    // Type identification
    QString getClassName() const override { return QStringLiteral("EDA_ZONE_DATA"); }
    QString getClass() const override { return QStringLiteral("ZONE"); }
    
    // Serialization - using QByteArray for cross-platform compatibility
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    
    // Connection status
    bool isConnected() const override;
    
    // Copy data
    void initDataFromSrcInCopyCtor(const EDA_ZONE_DATA& zone);
    
    // Conflict detection
    bool isConflicting() const;
    
    // Position
    QPointF getPosition() const override;
    void setPosition(const QPointF& pos) override;
    
    // Priority
    unsigned getAssignedPriority() const { return m_priority; }
    void setAssignedPriority(unsigned priority);
    
    bool higherPriority(const EDA_ZONE_DATA* other) const;
    bool sameNet(const EDA_ZONE_DATA* other) const;
    
    // Info
    void getMsgPanelInfo(QtEdaDrawFrame* frame, QVector<QtMsgPanelItem>& list) const;
    QString getFriendlyName() const override;

    // Helper functions for string conversion
    QString fillModeToString(QtZoneFillMode mode) const;
    QString connectionToString(QtZoneConnection connection) const;
    
    // Layer management
    void setLayerSet(const QtLayerSet& layerSet) override;
    QtLayerSet getLayerSet() const override { return m_layerSet; }
    void setLayerSetAndRemoveUnusedFills(const QtLayerSet& layerSet);
    
    // Zone name
    const QString& getZoneName() const { return m_zoneName; }
    void setZoneName(const QString& name);
    
    // Search
    bool matches(const QtSearchData& searchData, void* auxData) const;
    
    // Bounding box
    QRectF getBoundingBox() const override;
    void cacheBoundingBox();
    
    // Clearance
    std::optional<int> getLocalClearance() const;
    void setLocalClearance(std::optional<int> clearance);
    std::optional<int> getLocalClearance(QString* source) const;
    
    // Layer queries
    bool isOnCopperLayer() const override;
    void setLayer(QtPcbLayerId layer) override;
    QtPcbLayerId getLayer() const;
    QtPcbLayerId getFirstLayer() const;
    bool isOnLayer(QtPcbLayerId layer) const override;
    QVector<int> viewGetLayers() const;
    double viewGetLOD(int layer, const QtView* view) const override;
    
    // Fill mode
    void setFillMode(QtZoneFillMode fillMode);
    QtZoneFillMode getFillMode() const { return m_fillMode; }
    
    // Thermal relief
    void setThermalReliefGap(int gap);
    int getThermalReliefGap() const { return m_thermalReliefGap; }
    int getThermalReliefGap(EDA_PAD_DATA* pad, QString* source = nullptr) const;
    
    void setThermalReliefSpokeWidth(int width);
    int getThermalReliefSpokeWidth() const { return m_thermalReliefSpokeWidth; }
    
    // Area calculations
    double calculateFilledArea();
    double calculateOutlineArea();
    double getFilledArea() const { return m_area; }
    double getOutlineArea() const { return m_outlineArea; }
    
    // Thread safety
    QMutex& getLock() { return m_lock; }
    
    // Fill flags
    bool getFillFlag(QtPcbLayerId layer) const;
    void setFillFlag(QtPcbLayerId layer, bool flag);
    
    // Fill state
    bool isFilled() const { return m_isFilled; }
    void setIsFilled(bool filled);
    
    bool needRefill() const { return m_needRefill; }
    void setNeedRefill(bool needRefill);
    
    // Pad connection
    QtZoneConnection getPadConnection() const { return m_padConnection; }
    void setPadConnection(QtZoneConnection connection);
    
    // Min thickness
    int getMinThickness() const { return m_zoneMinThickness; }
    void setMinThickness(int thickness);
    
    // Hatch parameters
    int getHatchThickness() const { return m_hatchThickness; }
    void setHatchThickness(int thickness);
    
    int getHatchGap() const { return m_hatchGap; }
    void setHatchGap(int gap);
    
    QtEdaAngle getHatchOrientation() const { return m_hatchOrientation; }
    void setHatchOrientation(const QtEdaAngle& orientation);
    
    int getHatchSmoothingLevel() const { return m_hatchSmoothingLevel; }
    void setHatchSmoothingLevel(int level);
    
    double getHatchSmoothingValue() const { return m_hatchSmoothingValue; }
    void setHatchSmoothingValue(double value);
    
    double getHatchHoleMinArea() const { return m_hatchHoleMinArea; }
    void setHatchHoleMinArea(double area);
    
    int getHatchBorderAlgorithm() const { return m_hatchBorderAlgorithm; }
    void setHatchBorderAlgorithm(int algo);
    
    // Corner selection
    int getSelectedCorner() const;
    void setSelectedCorner(int corner);
    
    // Local flags
    int getLocalFlags() const { return m_localFlags; }
    void setLocalFlags(int flags) { m_localFlags = flags; }
    
    // Outline management
    QtShapePolySet* outline() { return m_poly.data(); }
    const QtShapePolySet* outline() const { return m_poly.data(); }
    void setOutline(QtShapePolySet* outline);
    
    // Effective shape
    std::shared_ptr<QtShape> getEffectiveShape(QtPcbLayerId layer = QtPcbLayerId::User1,
                                             int flash = 0) const;
    
    // Hit testing
    bool hitTest(const QPointF& position, int accuracy = 0) const;
    bool hitTestFilledArea(QtPcbLayerId layer, const QPointF& refPos, int accuracy = 0) const;
    bool hitTestCutout(const QPointF& refPos, int* outlineIdx = nullptr, int* holeIdx = nullptr) const;
    bool hitTest(const QRectF& rect, bool contained = true, int accuracy = 0) const;
    
    // Zone interactions
    void getInteractingZones(QtPcbLayerId layer, 
                           QVector<EDA_ZONE_DATA*>* sameNetCollidingZones,
                           QVector<EDA_ZONE_DATA*>* otherNetIntersectingZones) const;
    
    // Shape transformations
    void transformSolidAreasShapesToPolygon(QtPcbLayerId layer, QtShapePolySet& buffer) const;
    void transformSmoothedOutlineToPolygon(QtShapePolySet& buffer, int clearance,
                                         int error, int errorLoc,
                                         QtShapePolySet* boardOutline) const;
    void transformShapeToPolygon(QtShapePolySet& buffer, QtPcbLayerId layer,
                               int clearance, int error, int errorLoc,
                               bool ignoreLineWidth = false) const;
    
    // Hit test helpers
    bool hitTestForCorner(const QPointF& refPos, int accuracy,
                        QtShapePolySet::VertexIndex* cornerHit = nullptr) const;
    bool hitTestForEdge(const QPointF& refPos, int accuracy,
                      QtShapePolySet::VertexIndex* cornerHit = nullptr) const;
    
    // Fill management
    bool unFill();
    
    // Geometric transformations
    void move(const QPointF& offset) override;
    void moveEdge(const QPointF& offset, int edge);
    void rotate(const QPointF& center, const QtEdaAngle& angle);
    void flip(const QPointF& center, int flipDirection);
    void mirror(const QPointF& mirrorRef, int flipDirection);
    
    // Corner management
    int getNumCorners() const;
    const QPointF& getCornerPosition(int cornerIndex) const;
    void setCornerPosition(int cornerIndex, const QPointF& newPos);
    void newHole();
    bool appendCorner(const QPointF& position, int holeIdx, bool allowDuplication = false);
    
    // Border style
    QtZoneBorderDisplayStyle getHatchStyle() const { return m_borderStyle; }
    void setHatchStyle(QtZoneBorderDisplayStyle style);
    
    // Filled polygons
    bool hasFilledPolysForLayer(QtPcbLayerId layer) const;
    const QSharedPointer<QtShapePolySet>& getFilledPolysList(QtPcbLayerId layer) const;
    QtShapePolySet* getFill(QtPcbLayerId layer);
    void setFilledPolysList(QtPcbLayerId layer, const QtShapePolySet& polysList);
    void cacheTriangulation(QtPcbLayerId layer = QtPcbLayerId::User1);
    
    // Island management
    bool isIsland(QtPcbLayerId layer, int polyIdx) const;
    void setIsIsland(QtPcbLayerId layer, int polyIdx);
    
    // Smoothing
    bool buildSmoothedPoly(QtShapePolySet& smoothedPoly, QtPcbLayerId layer,
                         QtShapePolySet* boardOutline,
                         QtShapePolySet* smoothedPolyWithApron = nullptr) const;
    
    void setCornerSmoothingType(int type) { m_cornerSmoothingType = type; }
    int getCornerSmoothingType() const { return m_cornerSmoothingType; }
    
    void setCornerRadius(unsigned int radius);
    unsigned int getCornerRadius() const { return m_cornerRadius; }
    
    // Cutout management
    void removeCutout(int outlineIdx, int holeIdx);
    void addPolygon(QVector<QPointF>& polygon);
    void addPolygon(const QtShapeLineChain& polygon);
    void removeAllContours();
    
    // Description
    QString getItemDescription(QtUnitsProvider* unitsProvider, bool full) const override;
    QtBitmaps getMenuImage() const override;
    
    // Clone
    EDA_OBJECT_DATA* clone() const override;
    
    // Teardrop area
    bool isTeardropArea() const { return m_teardropType != QtTeardropType::TD_NONE; }
    void setTeardropAreaType(QtTeardropType type) { m_teardropType = type; }
    QtTeardropType getTeardropAreaType() const { return m_teardropType; }
    
    // Rule area properties
    bool hasKeepoutParametersSet() const;
    bool getIsRuleArea() const { return m_isRuleArea; }
    bool getRuleAreaPlacementEnabled() const { return m_ruleAreaPlacementEnabled; }
    QtRuleAreaPlacementSourceType getRuleAreaPlacementSourceType() const { return m_ruleAreaPlacementSourceType; }
    QString getRuleAreaPlacementSource() const { return m_ruleAreaPlacementSource; }
    bool getDoNotAllowCopperPour() const { return m_doNotAllowCopperPour; }
    bool getDoNotAllowVias() const { return m_doNotAllowVias; }
    bool getDoNotAllowTracks() const { return m_doNotAllowTracks; }
    bool getDoNotAllowPads() const { return m_doNotAllowPads; }
    bool getDoNotAllowFootprints() const { return m_doNotAllowFootprints; }
    
    void setIsRuleArea(bool enable);
    void setRuleAreaPlacementEnabled(bool enabled);
    void setRuleAreaPlacementSourceType(QtRuleAreaPlacementSourceType type);
    void setRuleAreaPlacementSource(const QString& source);
    void setDoNotAllowCopperPour(bool enable);
    void setDoNotAllowVias(bool enable);
    void setDoNotAllowTracks(bool enable);
    void setDoNotAllowPads(bool enable);
    void setDoNotAllowFootprints(bool enable);
    
    // Island removal
    QtIslandRemovalMode getIslandRemovalMode() const { return m_islandRemovalMode; }
    void setIslandRemovalMode(QtIslandRemovalMode mode);
    
    qint64 getMinIslandArea() const { return m_minIslandArea; }
    void setMinIslandArea(qint64 area);
    
    // Border hatch
    int getBorderHatchPitch() const;
    static int getDefaultHatchPitch();
    
    // Comparison (required by EDA_BOARD_OBJECT_DATA)
    double similarity(const EDA_BOARD_OBJECT_DATA& other) const override;
    bool operator==(const EDA_BOARD_OBJECT_DATA& other) const override;
    void setBorderDisplayStyle(QtZoneBorderDisplayStyle style, int pitch, bool rebuildHatch);
    void setBorderHatchPitch(int pitch);
    void unHatchBorder();
    void hatchBorder();
    const QVector<QtSeg>& getHatchLines() const { return m_borderHatchLines; }
    
    // Hash management
    void buildHashValue(QtPcbLayerId layer);
    QtHash128 getHashValue(QtPcbLayerId layer) const;
    
    // Comparison
    bool operator==(const EDA_ZONE_DATA& other) const;
    
    // Iterators
    // Iterators - using Qt container iterators
    auto begin() { return m_poly ? m_poly->getPolygons().begin() : QVector<QPolygonF>().begin(); }
    auto end() { return m_poly ? m_poly->getPolygons().end() : QVector<QPolygonF>().end(); }
    auto cbegin() const { return m_poly ? m_poly->getPolygons().cbegin() : QVector<QPolygonF>().cbegin(); }
    auto cend() const { return m_poly ? m_poly->getPolygons().cend() : QVector<QPolygonF>().cend(); }


protected:
    void swapData(EDA_BOARD_OBJECT_DATA* image) override;
    
private:
    void updateCachedBBox() const;
    void invalidateBBoxCache();
    
private:
    // Outline
    QSharedPointer<QtShapePolySet> m_poly;
    int m_cornerSmoothingType;
    unsigned int m_cornerRadius;
    
    // Zone name
    QString m_zoneName;
    
    // Layer set
    QtLayerSet m_layerSet;
    
    // Priority
    unsigned m_priority;
    
    // Rule area
    bool m_isRuleArea;
    bool m_ruleAreaPlacementEnabled;
    QtRuleAreaPlacementSourceType m_ruleAreaPlacementSourceType;
    QString m_ruleAreaPlacementSource;
    
    // Teardrop
    QtTeardropType m_teardropType;
    
    // Keepout rules
    bool m_doNotAllowCopperPour;
    bool m_doNotAllowVias;
    bool m_doNotAllowTracks;
    bool m_doNotAllowPads;
    bool m_doNotAllowFootprints;
    
    // Connection
    QtZoneConnection m_padConnection;
    int m_zoneClearance;
    int m_zoneMinThickness;
    int m_fillVersion;
    QtIslandRemovalMode m_islandRemovalMode;
    qint64 m_minIslandArea;
    
    // Fill state
    bool m_isFilled;
    bool m_needRefill;
    
    // Thermal relief
    int m_thermalReliefGap;
    int m_thermalReliefSpokeWidth;
    
    // Fill mode
    QtZoneFillMode m_fillMode;
    int m_hatchThickness;
    int m_hatchGap;
    QtEdaAngle m_hatchOrientation;
    int m_hatchSmoothingLevel;
    double m_hatchSmoothingValue;
    double m_hatchHoleMinArea;
    int m_hatchBorderAlgorithm;
    
    // Corner selection
    QSharedPointer<QtShapePolySet::VertexIndex> m_cornerSelection;
    int m_localFlags;
    
    // Filled polygons
    QMap<QtPcbLayerId, QSharedPointer<QtShapePolySet>> m_filledPolysList;
    
    // Fill flags
    QtLayerSet m_fillFlags;
    
    // Hash values
    QMap<QtPcbLayerId, QtHash128> m_filledPolysHash;
    
    // Border display
    QtZoneBorderDisplayStyle m_borderStyle;
    int m_borderHatchPitch;
    QVector<QtSeg> m_borderHatchLines;
    
    // Isolated islands
    QMap<QtPcbLayerId, QSet<int>> m_insulatedIslands;
    
    // Areas
    double m_area;
    double m_outlineArea;
    
    // Threading
    mutable QMutex m_lock;
    
    // Caching
    mutable QRectF m_cachedBBox;
    mutable bool m_bboxValid;
};

#endif // QT_ZONE_H